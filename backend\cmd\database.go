package main

import (
	"backend/internetal"
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

func InitDB() {
	// 加载配置
	config, err := loadConfig()
	if err != nil {
		panic("failed to load config: " + err.Error())
	}

	// 构建 MySQL 连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Base,
	)

	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("failed to connect database: " + err.Error())
	}

	// 自动迁移数据库表
	db.AutoMigrate(&internetal.Robot{})
	db.AutoMigrate(&internetal.ConversationHistory{})
	db.AutoMigrate(&internetal.ConversationMessage{})
	
	// 为现有记录添加默认消息类型
	db.Model(&internetal.ConversationMessage{}).Where("message_type = '' OR message_type IS NULL").Update("message_type", "text")
}

func GetDB() *gorm.DB {
	return db
}
