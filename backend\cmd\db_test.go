package main

import (
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"time"
)

type Robot struct {
	ID              int       `gorm:"primaryKey"`
	Name            string    `gorm:"column:name"`
	Description     string    `gorm:"column:description"`
	PersonnelDesign string    `gorm:"column:personnel_design"`
	CreateTime      time.Time `gorm:"column:create_time"`
	UpdateTime      time.Time `gorm:"column:update_time;autoUpdateTime"`
}

func InitTestDB() *gorm.DB {

	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	db.AutoMigrate(&Robot{}) // 替换成你的实体
	return db
}
