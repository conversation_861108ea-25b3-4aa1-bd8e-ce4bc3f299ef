package main

import (
    "backend/internetal"
    "net/http"
    "github.com/gin-gonic/gin"
)

// 保存对话历史
func SaveConversationHistory(c *gin.Context) {
    var req struct {
        AssistantID      int                           `json:"assistant_id"`
        AssistantName    string                        `json:"assistant_name"`
        Messages         []map[string]interface{}      `json:"messages"`
        ConversationID   *uint                         `json:"conversation_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
        return
    }

    db := GetDB()
    var conversation internetal.ConversationHistory

    if req.ConversationID != nil {
        // 更新现有对话 - 先清除旧消息
        db.First(&conversation, *req.ConversationID)
        db.Where("conversation_id = ?", *req.ConversationID).Delete(&internetal.ConversationMessage{})
    } else {
        // 创建新对话
        firstMessage := ""
        if len(req.Messages) > 0 {
            if content, ok := req.Messages[0]["content"].(string); ok {
                firstMessage = content
            }
        }

        conversation = internetal.ConversationHistory{
            AssistantID:   req.AssistantID,
            AssistantName: req.AssistantName,
            FirstMessage:  firstMessage,
        }
        db.Create(&conversation)
    }

    // 保存所有消息
    for _, msg := range req.Messages {
        messageType := "text"
        if msgType, ok := msg["messageType"].(string); ok {
            messageType = msgType
        }
        
        message := internetal.ConversationMessage{
            ConversationID: conversation.ID,
            Content:        msg["content"].(string),
            IsUser:         msg["isUser"].(bool),
            MessageType:    messageType,
        }
        if err := db.Create(&message).Error; err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "保存消息失败"})
            return
        }
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "msg":  "保存成功",
        "data": gin.H{"conversation_id": conversation.ID},
    })
}

// 获取历史对话列表
func GetHistoryList(c *gin.Context) {
    var req struct {
        AssistantID *int `json:"assistant_id"`
    }
    c.ShouldBindJSON(&req)

    db := GetDB()
    var histories []internetal.ConversationHistory

    query := db.Order("created_at DESC")
    if req.AssistantID != nil && *req.AssistantID > 0 {
        query = query.Where("assistant_id = ?", *req.AssistantID)
    }

    if err := query.Find(&histories).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "查询失败"})
        return
    }

    // 确保每个历史记录都有助手名称
    for i := range histories {
        if histories[i].AssistantName == "" {
            var robot internetal.Robot
            if err := db.First(&robot, histories[i].AssistantID).Error; err == nil {
                histories[i].AssistantName = robot.Name
            } else {
                histories[i].AssistantName = "未知助手"
            }
        }
    }

    c.JSON(http.StatusOK, gin.H{"code": 200, "data": histories})
}

// 获取对话消息
func GetHistoryMessages(c *gin.Context) {
    var req struct {
        ConversationID uint `json:"conversation_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
        return
    }

    db := GetDB()
    var messages []internetal.ConversationMessage

    if err := db.Where("conversation_id = ?", req.ConversationID).Order("created_at ASC").Find(&messages).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "查询失败"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"code": 200, "data": messages})
}

// 删除历史对话
func DeleteHistory(c *gin.Context) {
    var req struct {
        ConversationID uint `json:"conversation_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数错误"})
        return
    }

    db := GetDB()
    
    // 删除消息
    db.Where("conversation_id = ?", req.ConversationID).Delete(&internetal.ConversationMessage{})
    // 删除对话记录
    db.Delete(&internetal.ConversationHistory{}, req.ConversationID)

    c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}
