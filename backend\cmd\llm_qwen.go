package main

import (
	"backend/internetal/utils"
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type Tool struct {
	Type     string                   `json:"type"`
	Function utils.FunctionDefinition `json:"function"`
}

// 修正请求体结构，与前端发送的格式匹配
type TextSendRequest struct {
	UserID    string        `json:"user_id"`   // 对应前端的user_id
	Message   utils.Message `json:"message"`   // 对应前端的message
	Streaming bool          `json:"streaming"` // 保持原有流式参数
}

// 统一响应结构
type ApiResponse struct {
	Code int    `json:"code"`
	Data string `json:"data"`
	Msg  string `json:"msg"`
}

type RobotBody struct {
	Model    string          `json:"model"`
	Messages []utils.Message `json:"messages"`
	Tools    []Tool          `json:"tools"`
	Stream   bool            `json:"stream"`
}

type Delta struct {
	Content string `json:"content"`
}

type Choice struct {
	Delta        Delta         `json:"delta"`
	Message      utils.Message `json:"message"`
	FinishReason string        `json:"finish_reason"`
	Index        int           `json:"index"`
}

type Response struct {
	Choices []Choice `json:"choices"`
}

var apiKey string = "sk-64c4dbccf0c34ab4a9dcade27d5edd32"
var url1 string = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
var modelName string = "qwen-plus"
var sessionStore = utils.GetSessionStore()

func LlmInteraction(c *gin.Context) {
	client := &http.Client{}
	var fullContent string
	var req TextSendRequest // 使用修正后的请求体结构
	var questions utils.Message
	var messages []utils.Message
	var streaming bool

	// 1. 解析前端请求体（修正：使用匹配的结构体）
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "请求体解析失败: " + err.Error(),
			Data: "",
		})
		return
	}

	// 2. 提取请求参数（修正：使用新结构体的字段）
	streaming = req.Streaming
	questions = req.Message
	if questions.Content == "" {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "消息内容不能为空",
			Data: "",
		})
		return
	}

	// 3. 处理会话存储（修正：使用user_id作为会话标识）
	store := sessionStore
	if store == nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "会话存储初始化失败",
			Data: "",
		})
		return
	}
	messagesStore := store.GetMessageStore(req.UserID)
	messages = messagesStore.MessageStore
	messages = append(messages, questions)
	store.AddMessage(req.UserID, questions)

	// 4. 处理system类型消息（保持原有逻辑，优化响应格式）
	if questions.Role == "system" {
		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "设置成功",
			Data: "",
		})
		return
	}

	// 5. 定义天气工具（保持原有逻辑）
	weatherFunction := Tool{
		Type: "function",
		Function: utils.FunctionDefinition{
			Name:        "get_weather",
			Description: "获取指定城市的天气信息",
			Parameters: json.RawMessage([]byte(`{
              "type": "object",
              "properties": {
              "city": {
                 "type": "string",
                 "description": "要查询天气的城市名称"
                  }
             },
                  "required": ["city"]
            }`)),
		},
	}

	// 6. 先尝试知识库搜索（优先级最高）
	knowledgeBaseResults := searchKnowledgeBaseContent(questions.Content, []string{}, 5, 0.3)

	// 如果知识库有相关内容，直接返回
	if len(knowledgeBaseResults) > 0 {
		// 构建基于知识库的回复
		knowledgeResponse := buildKnowledgeBaseResponse(questions.Content, knowledgeBaseResults)

		// 保存机器人回复
		answerBody := utils.Message{
			Role:    "assistant",
			Content: knowledgeResponse,
		}
		store.AddMessage(req.UserID, answerBody)

		c.JSON(http.StatusOK, ApiResponse{
			Code: 200,
			Msg:  "success (knowledge base)",
			Data: knowledgeResponse,
		})
		return
	}

	// 7. 如果知识库没有相关内容，构建AI请求体
	requestBody := RobotBody{
		Model:    modelName,
		Messages: messages,
		Tools:    []Tool{weatherFunction},
		Stream:   streaming,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "请求体序列化失败: " + err.Error(),
			Data: "",
		})
		return
	}

	// 7. 调用第三方AI接口（增加响应状态检查）
	reqAI, err := http.NewRequest("POST", url1, bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "创建AI请求失败: " + err.Error(),
			Data: "",
		})
		return
	}

	reqAI.Header.Set("Authorization", "Bearer "+apiKey)
	reqAI.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(reqAI)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "调用AI模型失败: " + err.Error(),
			Data: "",
		})
		return
	}
	defer resp.Body.Close()

	// 关键修复：检查第三方API响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  fmt.Sprintf("AI服务返回错误 (状态码: %d): %s", resp.StatusCode, string(body)),
			Data: "",
		})
		return
	}
}


	// 6. 构建AI请求体（保持原有逻辑）
	requestBody := RobotBody{
		Model:    modelName,
		Messages: messages,
		Tools:    []Tool{weatherFunction},
		Stream:   streaming,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		c.JSON(http.StatusBadRequest, ApiResponse{
			Code: 400,
			Msg:  "请求体序列化失败: " + err.Error(),
			Data: "",
		})
		return
	}

	// 7. 调用第三方AI接口（增加响应状态检查）
	reqAI, err := http.NewRequest("POST", url1, bytes.NewBuffer(jsonData))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "创建AI请求失败: " + err.Error(),
			Data: "",
		})
		return
	}

	reqAI.Header.Set("Authorization", "Bearer "+apiKey)
	reqAI.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(reqAI)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  "调用AI模型失败: " + err.Error(),
			Data: "",
		})
		return
	}
	defer resp.Body.Close()

	// 关键修复：检查第三方API响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		c.JSON(http.StatusInternalServerError, ApiResponse{
			Code: 500,
			Msg:  fmt.Sprintf("AI服务返回错误 (状态码: %d): %s", resp.StatusCode, string(body)),
			Data: "",
		})
		return
	}

	// 8. 处理流式响应（保持原有逻辑，优化响应头）
	if streaming {
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		c.Writer.WriteHeader(http.StatusOK)

		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.HasPrefix(line, "data:") {
				data := strings.TrimSpace(line[5:])
				if data == "DONE" {
					break
				}
				var chunk Response
				if err := json.Unmarshal([]byte(data), &chunk); err != nil {
					continue // 忽略解析失败的chunk
				}
				if len(chunk.Choices) > 0 {
					content := chunk.Choices[0].Delta.Content
					if content != "" {
						c.Writer.WriteString(content + "\n")
						c.Writer.Flush()
						fullContent += content
					}
				}
			}
		}
	} else {
		// 9. 处理非流式响应（增加索引越界检查）
		bodyText, err := io.ReadAll(resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "读取AI响应失败: " + err.Error(),
				Data: "",
			})
			return
		}

		var res Response
		if err := json.Unmarshal(bodyText, &res); err != nil {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "解析AI响应失败: " + err.Error(),
				Data: "",
			})
			return
		}

		// 关键修复：检查Choices长度，避免索引越界
		if len(res.Choices) == 0 {
			c.JSON(http.StatusInternalServerError, ApiResponse{
				Code: 500,
				Msg:  "AI响应内容为空",
				Data: "",
			})
			return
		}

		// 处理工具调用（保持原有逻辑）
		if len(res.Choices[0].Message.ToolCalls) > 0 {
			for _, toolCall := range res.Choices[0].Message.ToolCalls {
				if toolCall.Function.Name == "get_weather" {
					var rawArgs map[string]interface{}
					var argsStr string
					if err := json.Unmarshal(toolCall.Function.Arguments, &argsStr); err != nil {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "解析工具调用参数字符串失败: " + err.Error(),
							Data: "",
						})
						return
					}

					// 再对字符串进行 json 解析
					if err := json.Unmarshal([]byte(argsStr), &rawArgs); err != nil {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "解析工具调用参数失败: " + err.Error(),
							Data: "",
						})
						return
					}

					city, ok := rawArgs["city"].(string)
					if !ok {
						c.JSON(http.StatusInternalServerError, ApiResponse{
							Code: 500,
							Msg:  "城市参数解析失败",
							Data: "",
						})
						return
					}

					weather := GetNowWeather(city)
					fullContent = FormatWeatherResponse(city, []byte(weather))

					c.JSON(http.StatusOK, ApiResponse{
						Code: 200,
						Msg:  "success",
						Data: fullContent,
					})
					return
				}
			}
		} else {
			// 普通文本响应
			fullContent = res.Choices[0].Message.Content
			c.JSON(http.StatusOK, ApiResponse{
				Code: 200,
				Msg:  "success",
				Data: fullContent,
			})
		}
	}

	// 10. 保存对话记录（保持原有逻辑）
	answerBody := utils.Message{
		Role:    "assistant",
		Content: fullContent,
	}
	store.AddMessage(req.UserID, answerBody)
}
