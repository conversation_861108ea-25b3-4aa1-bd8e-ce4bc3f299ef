package main

import (
	"backend/internetal/utils"
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// 简化测试：仅替换必要依赖
func TestLlmInteraction_SimpleCases(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 测试用例集合
	testCases := []struct {
		name         string
		requestBody  TextSendRequest
		expectedCode int
		expectedMsg  string
	}{
		{
			name: "空消息内容",
			requestBody: TextSendRequest{
				UserID: "test123",
				Message: utils.Message{
					Role:    "user",
					Content: "",
				},
				Streaming: false,
			},
			expectedCode: 400,
			expectedMsg:  "消息内容不能为空",
		},
		{
			name: "system消息处理",
			requestBody: TextSendRequest{
				UserID: "test123",
				Message: utils.Message{
					Role:    "system",
					Content: "初始化",
				},
				Streaming: false,
			},
			expectedCode: 200,
			expectedMsg:  "设置成功",
		},
	}

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建测试请求
			jsonBody, _ := json.Marshal(tc.requestBody)
			req := httptest.NewRequest("POST", "/llm", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 创建Gin上下文
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 执行处理函数
			LlmInteraction(c)

			// 基础断言
			assert.Equal(t, tc.expectedCode, w.Code)

			// 解析响应体验证
			var resp ApiResponse
			err := json.Unmarshal(w.Body.Bytes(), &resp)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedCode, resp.Code)
			assert.Contains(t, resp.Msg, tc.expectedMsg)
		})
	}
}

// 测试正常用户消息流程（模拟AI响应）
func TestLlmInteraction_UserMessage(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 构造请求
	reqBody := TextSendRequest{
		UserID: "test456",
		Message: utils.Message{
			Role:    "user",
			Content: "你好",
		},
		Streaming: false,
	}
	jsonBody, _ := json.Marshal(reqBody)
	req := httptest.NewRequest("POST", "/llm", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 执行测试
	LlmInteraction(c)

	// 验证响应格式（不验证具体内容，因为依赖外部API）
	assert.Equal(t, http.StatusOK, w.Code)
	var resp ApiResponse
	json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Equal(t, 200, resp.Code)
}
