package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"runtime"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"github.com/goccy/go-json"
	"github.com/gorilla/websocket"
	"gopkg.in/yaml.v2"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Config struct {
	Database struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Username string `yaml:"username"`
		Password string `yaml:"password"`
		Base     string `yaml:"base"`
	} `yaml:"database"`
}

var (
	frontendUrl string = "localhost:8081"
	rustUrl     string = "ws://**************:8080/call/webrtc"
)

// 连接数据库
var SqlSession *gorm.DB

func InitAql(config *Config) {
	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Database.Username,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.Base,
	)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	SqlSession = db
}

func loadConfig() (*Config, error) {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	configPath := filepath.Join(basepath, "config.yaml")
	log.Printf("basepath: %s", basepath)
	log.Printf("configPath: %s", configPath)
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}
	return &config, nil
}

func main() {

	r := gin.Default()
	// 配置 CORS 中间件（作用于当前 r）
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:8081","http://localhost:3001", "http://localhost:3002",},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// 初始化数据库（注意：你的 InitDB 还没贴，确保这里没问题）
	InitDB()

	// 加载配置（如果需要
	// 添加提示词优化路由
	prompt := r.Group("/api/prompt")
	{
		prompt.POST("/optimize", OptimizePrompt)
		prompt.POST("/optimize-debug", OptimizeBasedOnDebug)
	}

	// 添加知识库相关路由
	kb := r.Group("/api/knowledge-base")
	{
		kb.POST("/analyze-document", AnalyzeDocument)
		kb.POST("/segment-document", SegmentDocument)
		kb.POST("/extract-keywords", ExtractKeywords)
		kb.POST("/generate-summary", GenerateSummary)
		kb.POST("/batch-process", BatchProcessDocuments)
		kb.POST("/search", SearchKnowledgeBase)
		kb.POST("/create", CreateKnowledgeBase)
		kb.POST("/save", SaveKnowledgeBase)
		kb.POST("/list", ListKnowledgeBases)
		kb.POST("/delete", DeleteKnowledgeBase)
	}



	config, err := loadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	InitAql(config)
	robot := r.Group("/api/robot")
	{
		robot.POST("/create", CreateRobotRepository)
		robot.POST("/find/by_id", FindByIdRepository)
		robot.POST("/find/by_name", FindByNameRepository)
		robot.POST("/update", UpdateRobotRepository)
		robot.POST("/delete", DeleteRobotRepository)
		robot.POST("/list", ListRobotsRepository)
	}

	// 添加历史记录路由
	history := r.Group("/api/history")
	{
		history.POST("/save", SaveConversationHistory)
		history.POST("/list", GetHistoryList)
		history.POST("/messages", GetHistoryMessages)
		history.POST("/delete", DeleteHistory)
	}

	llm := r.Group("api/llm")
	{
		llm.POST("/communication", LlmInteraction)
	}
	r.POST("/api/voice/settings", handleVoiceSettings)
	r.GET("/api/voice/settings", handleGetVoiceSettings)
	r.POST("/api/tts/test", handleTTSTest)

	// 语音包管理相关API
	r.GET("/api/voice/packages/list", handleListVoicePackages)
	r.GET("/api/voice/packages/download", handleDownloadVoicePackage)
	r.POST("/api/voice/packages/upload", handleUploadVoicePackage)

	r.GET("/ws", handleConnection)

	r.POST("/api/text/send", func(c *gin.Context) {
		LlmInteraction(c)
	}) // 注意端口号
	r.Run(":8080")

}

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 检查请求来源，这里设置为允许所有来源
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func handleConnection(c *gin.Context) {

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Println("Error upgrading connection:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade connection"})
		return
	}
	defer conn.Close()
	ctx, cancel := context.WithCancel(c)
	defer cancel()
	for {
		_, message, err := conn.ReadMessage()
		var req WebRTCMessage
		if err := json.Unmarshal(message, &req); err != nil {
			log.Printf("无法解析消息: %v, 原始消息: %s", err, string(message))
			continue
		}
		err = json.Unmarshal(message, &req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade connection"})
			return
		}
		fmt.Println(string(message))
		if req.Type == "offer" {
			log.Printf("处理offer请求，当前语音设置: Language=%s, Voice=%s",
				globalVoiceSettings.Language, globalVoiceSettings.Voice)

			// 使用全局语音设置，并转换为腾讯云支持的VoiceType
			tencentVoiceType := getTencentVoiceType(globalVoiceSettings.Language, globalVoiceSettings.Voice)
			log.Printf("最终使用的腾讯云VoiceType: %s", tencentVoiceType)
			NewSignalingClient(conn, ctx, rustUrl, PBXMessage{
				Command: "invite",
				Option: &CallOptions{
					Asr: &AsrConfig{
						ModelType: "16k_zh",
						Endpoint:  "asr.tencentapi.com",
						Language:  globalVoiceSettings.Language,
						Provider:  "tencent",
						AppId:     "**********",
						SecretId:  "AKIDqfPySltodHk3WzRoyywseCZkiSyHPK3b",
						SecretKey: "PaWFk26w12tX2LSPAlBRXUI7oKgojPJ2",
					},
					Tts: &TtsConfig{
						Endpoint:  "tts.tencentapi.com",
						Provider:  "tencent",
						Speaker:   tencentVoiceType,
						Speed:     float32(globalVoiceSettings.Rate),
						Volume:    globalVoiceSettings.Volume,
						AppId:     "**********",
						SecretId:  "AKIDqfPySltodHk3WzRoyywseCZkiSyHPK3b",
						SecretKey: "PaWFk26w12tX2LSPAlBRXUI7oKgojPJ2",
					},
					Offer: req.SDP,
				},
			})
		}
	}
}

type SignalingClient struct {
	outConn      *websocket.Conn
	conn         *websocket.Conn
	ctx          context.Context
	cancel       context.CancelFunc
	recvDone     chan struct{}
	isTTSPlaying bool // 新增字段
}

type PBXMessage struct {
	Command string       `json:"command"` // 操作类型，如 'invite', 'tts'
	Option  *CallOptions `json:"option,omitempty"`
	Text    string       `json:"text,omitempty"`
	PlayId  string       `json:"playId,omitempty"`
}

// CallOptions 包含呼叫配置的详细信息
type CallOptions struct {
	Asr   *AsrConfig `json:"asr,omitempty"`   // 自动语音识别配置
	Tts   *TtsConfig `json:"tts,omitempty"`   // 语音合成配置
	Offer string     `json:"offer,omitempty"` // SDP Offer 信息
}

// AsrConfig 自动语音识别配置
type AsrConfig struct {
	ModelType string `json:"modelType"`
	Endpoint  string `json:"endpoint"`
	Provider  string `json:"provider"`
	AppId     string `json:"appId"`
	SecretId  string `json:"secretId"`
	SecretKey string `json:"secretKey"`
	Language  string `json:"language"`
}

// TtsConfig 文本转语音配置
type TtsConfig struct {
	Endpoint  string  `json:"endpoint"`
	Provider  string  `json:"provider"`
	Speaker   string  `json:"speaker"`
	AppId     string  `json:"appId"`
	SecretId  string  `json:"secretId"`
	SecretKey string  `json:"secretKey"`
	Speed     float32 `json:"speed"`
	Volume    int     `json:"volume"`
}

// EventMessage 表示服务端发送的事件通知
type EventMessage struct {
	Event     string                 `json:"event"`
	TrackId   string                 `json:"trackId,omitempty"`
	Timestamp *uint64                `json:"timestamp,omitempty"`
	Key       string                 `json:"key,omitempty"`
	Duration  uint32                 `json:"duration,omitempty"`
	SDP       string                 `json:"sdp,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Text      string                 `json:"text,omitempty"`
}

type WebRTCMessage struct {
	Type string `json:"type"`          // 消息类型: offer / answer / ice-candidate
	SDP  string `json:"sdp,omitempty"` // SDP 内容（仅 offer / answer 时有）
	Text string `json:"text,omitempty"`
}

func NewSignalingClient(Conn *websocket.Conn, ctx context.Context, serverAddr string, initial PBXMessage) (*SignalingClient, error) {
	ctx, cancel := context.WithCancel(ctx)

	conn, _, err := websocket.DefaultDialer.Dial(serverAddr, nil)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("websocket dial failed: %w", err)
	}

	msg, err := json.Marshal(initial)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to marshal initial PBXMessage: %w", err)
	}
	if err := conn.WriteMessage(websocket.TextMessage, msg); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to send initial message: %w", err)
	}
	log.Println("Send invite command to RustPBX....")

	client := &SignalingClient{
		outConn:  Conn,
		conn:     conn,
		ctx:      ctx,
		cancel:   cancel,
		recvDone: make(chan struct{}),
	}
	go client.listen()
	return client, nil
}

func (s *SignalingClient) listen() {
	defer close(s.recvDone)
	client := NewOpenAIClient("5cd96106bc3a488a8833da74f2bfaced.z7C5LFWGcBhl6nYT")
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			typeVal, data, err := s.conn.ReadMessage()
			if err != nil {
				return
			}
			if typeVal != websocket.TextMessage {
				continue
			}
			log.Println("received:", string(data))
			var evt EventMessage
			if err := json.Unmarshal(data, &evt); err != nil {
				continue
			}
			s.handleEvent(evt, client)
		}
	}
}

func (s *SignalingClient) handleEvent(evt EventMessage, llm *OpenAIClient) {

	switch evt.Event {
	case "answer":
		var message = WebRTCMessage{
			SDP:  evt.SDP,
			Type: "answer",
		}
		marshal, _ := json.Marshal(message)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

		// 使用当前语音设置发送TTS
		log.Printf("使用语音设置: Language=%s, Voice=%s, Rate=%.1f, Volume=%d",
			globalVoiceSettings.Language, globalVoiceSettings.Voice,
			globalVoiceSettings.Rate, globalVoiceSettings.Volume)

		// 根据语言选择合适的问候语
		greetingText := getGreetingByLanguage(globalVoiceSettings.Language)

		resp := PBXMessage{
			Command: "tts",
			Text:    greetingText,
		}
		marshal, _ = json.Marshal(resp)
		s.conn.WriteMessage(websocket.TextMessage, marshal)
	case "asrFinal":
		// 发送用户ASR结果（保持原文）
		var message = WebRTCMessage{
			Text: evt.Text, // 用户输入保持原文
			Type: "asrFinal",
		}
		marshal, _ := json.Marshal(message)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

		// 获取AI回复（使用原始中文进行思考）
		chineseReply, _ := llm.GenerateText("请回答", evt.Text, "", "")

		// 翻译AI回复到目标语言
		var aiDisplayText string
		if globalVoiceSettings.Language == "zh-cn" {
			aiDisplayText = chineseReply
		} else {
			aiDisplayText = translateForTTS(chineseReply, globalVoiceSettings.Language)
			log.Printf("AI回复翻译: %s -> %s", chineseReply, aiDisplayText)
		}

		// 发送翻译后的AI回复
		aiMessage := WebRTCMessage{
			Text: aiDisplayText, // 发送翻译后的AI回复
			Type: "aiResponse",
		}
		aiMarshal, _ := json.Marshal(aiMessage)
		s.outConn.WriteMessage(websocket.TextMessage, aiMarshal)

		// 发送TTS开始信号
		ttsStartMessage := WebRTCMessage{
			Type: "ttsStart",
		}
		ttsStartMarshal, _ := json.Marshal(ttsStartMessage)
		s.outConn.WriteMessage(websocket.TextMessage, ttsStartMarshal)

		// 发送TTS命令（使用翻译后的文本）
		resp := PBXMessage{
			Command: "tts",
			Text:    aiDisplayText, // TTS也使用翻译后的文本
		}
		marshal, _ = json.Marshal(resp)
		s.conn.WriteMessage(websocket.TextMessage, marshal)
	case "ttsComplete":
		// TTS播放完成，发送信号给前端
		ttsEndMessage := WebRTCMessage{
			Type: "ttsEnd",
		}
		marshal, _ := json.Marshal(ttsEndMessage)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

	case "asrDelta":
		// ASR实时识别结果，可以选择处理或忽略
		log.Printf("ASR实时识别: %s", evt.Text)

	case "metrics":
		// 性能指标，记录日志即可
		log.Printf("性能指标: %s", evt.Key)

	case "trackStart":
		log.Printf("音轨开始: %s", evt.TrackId)

	case "trackEnd":
		log.Printf("音轨结束: %s", evt.TrackId)

	case "error":
		log.Printf("TTS错误: %s", evt.Data)
		// 发送错误信息给前端
		errorMessage := WebRTCMessage{
			Type: "error",
			Text: fmt.Sprintf("TTS错误: %v", evt.Data),
		}
		marshal, _ := json.Marshal(errorMessage)
		s.outConn.WriteMessage(websocket.TextMessage, marshal)

	default:
		log.Printf("Unhandled event: %s", evt.Event)
	}

}
func getGreetingByLanguage(language string) string {
	switch language {
	case "zh-cn":
		return "我是语音助手"
	case "en-us":
		return "Hello, I am your voice assistant"
	case "ja-jp":
		return "こんにちは、私は音声アシスタントです"
	case "ko-kr":
		return "안녕하세요, 저는 음성 어시스턴트입니다"
	case "fr-fr":
		return "Bonjour, je suis votre assistant vocal"
	case "de-de":
		return "Hallo, ich bin Ihr Sprachassistent"
	default:
		return "我是语音助手"
	}
}

// getTencentVoiceType 将前端voice参数转换为腾讯云TTS支持的VoiceType
func getTencentVoiceType(language, voice string) string {
	// 腾讯云TTS支持的VoiceType映射
	voiceMap := map[string]map[string]string{
		"zh-cn": {
			"601002": "101001", // 智瑜 - 温暖女声
			"601003": "101002", // 智聆 - 通用女声
			"601004": "101003", // 智美 - 甜美女声
			"601005": "101004", // 智云 - 通用男声
			"601006": "101005", // 智莉 - 通用女声
			"601007": "101006", // 智言 - 助手女声
		},
		"en-us": {
			"601101": "1001", // Emma (Female) - 英文女声
			"601102": "1002", // Brian (Male) - 英文男声
			"601103": "1050", // Amy (Female) - 英文女声
			"601104": "1051", // Russell (Male) - 英文男声
		},
		"ja-jp": {
			"601201": "1003", // さくら (Female) - 日文女声
			"601202": "1004", // たかし (Male) - 日文男声
		},
		"ko-kr": {
			"601301": "1005", // 지은 (Female) - 韩文女声
			"601302": "1006", // 민수 (Male) - 韩文男声
		},
		"fr-fr": {
			"601401": "1007", // Céline (Female) - 法文女声
			"601402": "1008", // Pierre (Male) - 法文男声
		},
		"de-de": {
			"601501": "1009", // Anna (Female) - 德文女声
			"601502": "1010", // Hans (Male) - 德文男声
		},
	}

	if langMap, exists := voiceMap[language]; exists {
		if tencentVoice, exists := langMap[voice]; exists {
			log.Printf("语音映射: %s-%s -> %s", language, voice, tencentVoice)
			return tencentVoice
		}
	}

	// 默认返回中文女声
	log.Printf("使用默认语音: 101001 (原始: %s-%s)", language, voice)
	return "101001"
}

// translateForTTS 专门为TTS翻译文本
func translateForTTS(chineseText, targetLanguage string) string {
	if targetLanguage == "zh-cn" {
		return chineseText
	}

	// 构建翻译提示词
	var translatePrompt string
	switch targetLanguage {
	case "en-us":
		translatePrompt = "Translate this Chinese text to natural English:"
	case "ja-jp":
		translatePrompt = "この中国語を自然な日本語に翻訳してください:"
	case "ko-kr":
		translatePrompt = "이 중국어를 자연스러운 한국어로 번역해주세요:"
	case "fr-fr":
		translatePrompt = "Traduisez ce texte chinois en français naturel:"
	case "de-de":
		translatePrompt = "Übersetzen Sie diesen chinesischen Text ins natürliche Deutsche:"
	default:
		return chineseText
	}

	// 使用现有的LLM客户端进行翻译
	client := NewOpenAIClient("5cd96106bc3a488a8833da74f2bfaced.z7C5LFWGcBhl6nYT")
	translatedText, err := client.GenerateText(translatePrompt, chineseText, "", "")
	if err != nil {
		log.Printf("翻译失败: %v, 使用原文", err)
		return chineseText
	}

	return translatedText
}



// translateForDisplay 为界面显示翻译文本（复用translateForTTS的逻辑）
