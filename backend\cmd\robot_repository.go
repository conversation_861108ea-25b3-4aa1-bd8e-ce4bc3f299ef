package main

import (
	"backend/internetal"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 创建机器人
func CreateRobotRepository(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBind<PERSON>(&robot); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	createdRobot, err := CreateRobotService(robot)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": createdRobot, "msg": "创建成功"})
}

// 删除机器人（从 JSON 体读取 id）
func DeleteRobotRepository(c *gin.Context) {
	var req struct {
		ID int `json:"id"`
	}
	if err := c.<PERSON>(&req); err != nil || req.ID == 0 {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少或非法的id"})
		return
	}

	err := DeleteRobotService(req.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "删除失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

// 通过ID查找机器人，id通过query参数传递
func FindByIdRepository(c *gin.Context) {
	idStr := c.Query("id")
	id, err := strconv.Atoi(idStr)
	if err != nil || id == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少或非法的id"})
		return
	}

	robot, err := FindByIdService(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

// 通过名称查找机器人，name通过query参数传递
func FindByNameRepository(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "缺少name参数"})
		return
	}

	robot, err := FindByNameService(name)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "未查找到相关机器人"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

// 更新机器人
func UpdateRobotRepository(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil || robot.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败或缺少id"})
		return
	}

	err := UpdateRobotService(robot)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "更新失败"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "更新成功"})
}

// 列表查询机器人
func ListRobotsRepository(c *gin.Context) {
	robots, err := ListRobotsService()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "msg": "获取列表失败"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robots})
}
