package main

import (
	"backend/internetal"
	"time"
)

var robotStorage = make(map[int]internetal.Robot)
var robotIDCounter = 1

func CreateRobotService(robot internetal.Robot) (internetal.Robot, error) {
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = "你是一个友善、专业的AI助手。"
	}
	if robot.ReplyLogic == "" {
		robot.ReplyLogic = `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`
	}
	if robot.KnowledgeConfig == "" {
		robot.KnowledgeConfig = `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`
	}
	robot.ID = robotIDCounter
	robotIDCounter++
	robot.CreateTime = time.Now()
	robot.UpdateTime = time.Now()
	robotStorage[robot.ID] = robot
	return robot, nil
}

func DeleteRobotService(id int) error {
	result := SqlSession.Delete(&internetal.Robot{}, id)
	return result.Error
}

func FindByIdService(id int) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.First(&robot, id)
	return robot, result.Error
}

func FindByNameService(name string) (internetal.Robot, error) {
	var robot internetal.Robot
	result := SqlSession.Where("name = ?", name).First(&robot)
	return robot, result.Error
}

func UpdateRobotService(robot internetal.Robot) error {
	var findAssistant internetal.Robot
	result := SqlSession.First(&findAssistant, robot.ID)
	if result.Error != nil {
		return result.Error
	}
	if robot.Name == "" {
		robot.Name = findAssistant.Name
	}
	if robot.Description == "" {
		robot.Description = findAssistant.Description
	}
	if robot.PersonnelDesign == "" {
		robot.PersonnelDesign = findAssistant.PersonnelDesign
	}
	robot.UpdateTime = time.Now()
	return SqlSession.Model(&findAssistant).Updates(robot).Error
}

func ListRobotsService() ([]internetal.Robot, error) {
	// 如果内存中没有机器人，创建一个默认的
	if len(robotStorage) == 0 {
		defaultRobot := internetal.Robot{
			ID:              1,
			Name:            "默认助手",
			Description:     "默认AI助手",
			PersonnelDesign: "你是一个友善、专业的AI助手。",
			ReplyLogic:      `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`,
			KnowledgeConfig: `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`,
			CreateTime:      time.Now(),
			UpdateTime:      time.Now(),
		}
		robotStorage[1] = defaultRobot
		robotIDCounter = 2
	}

	var robots []internetal.Robot
	for _, robot := range robotStorage {
		robots = append(robots, robot)
	}
	return robots, nil
}
