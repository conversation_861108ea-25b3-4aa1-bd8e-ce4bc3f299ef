package main

import (
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestCreateRobot(t *testing.T) {
	// 初始化数据库连接
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatal(err)
	}
	SqlSession = db // 替换全局变量

	// 创建测试路由
	r := gin.Default()
	r.POST("/api/robot/create", CreateRobotRepository)

	// 发送请求并检查响应
	reqBody := `{"name": "TestRobot"}`
	req := httptest.NewRequest("POST", "/api/robot/create", strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	resp := httptest.NewRecorder()

	r.ServeHTTP(resp, req)

	if resp.Code != http.StatusOK {
		t.Errorf("期望状态码200，但得到：%d", resp.Code)
	}
}
