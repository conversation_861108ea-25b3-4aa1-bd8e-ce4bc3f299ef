package main

import (
	"backend/internetal"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// 内存存储 - 使用 robot_servicce.go 中声明的变量

// simpleMain 是一个简化的服务器实现
// 重命名以避免与 main.go 中的 main 函数冲突
func simpleMain() {
	r := gin.Default()

	// 配置 CORS 中间件
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:8081"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// 初始化默认机器人
	initDefaultRobot()

	// 机器人相关路由
	robot := r.Group("/api/robot")
	{
		robot.POST("/create", createRobot)
		robot.POST("/find/by_id", findByID)
		robot.POST("/find/by_name", findByName)
		robot.POST("/update", updateRobot)
		robot.POST("/delete", deleteRobot)
		robot.POST("/list", listRobots)
	}

	r.Run(":8081")
}

func initDefaultRobot() {
	defaultRobot := internetal.Robot{
		ID:              1,
		Name:            "默认助手",
		Description:     "默认AI助手",
		PersonnelDesign: "你是一个友善、专业的AI助手。",
		ReplyLogic:      `{"systemRole":"你是一个专业的AI助手","dialogCommand":"请以简洁、专业的方式回答用户的问题","responseMode":"simple","temperature":0.6,"maxTokens":50,"language":"zh-cn","speaker":"601002","speechSpeed":1.0}`,
		KnowledgeConfig: `{"callMethod":"auto","searchStrategy":"mixed","maxRecall":5,"minScore":0.50,"queryRewrite":true,"resultRerank":true}`,
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
	}
	robotStorage[1] = defaultRobot
	robotIDCounter = 2
}

func createRobot(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	robot.ID = robotIDCounter
	robotIDCounter++
	robot.CreateTime = time.Now()
	robot.UpdateTime = time.Now()
	robotStorage[robot.ID] = robot

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot, "msg": "创建成功"})
}

func findByID(c *gin.Context) {
	var req struct {
		ID int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	robot, exists := robotStorage[req.ID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
}

func findByName(c *gin.Context) {
	var req struct {
		Name string `json:"name"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	for _, robot := range robotStorage {
		if robot.Name == req.Name {
			c.JSON(http.StatusOK, gin.H{"code": 200, "data": robot})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
}

func updateRobot(c *gin.Context) {
	var robot internetal.Robot
	if err := c.ShouldBindJSON(&robot); err != nil || robot.ID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败或缺少id"})
		return
	}

	existing, exists := robotStorage[robot.ID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"code": 404, "msg": "机器人不存在"})
		return
	}

	// 更新字段
	if robot.Name != "" {
		existing.Name = robot.Name
	}
	if robot.Description != "" {
		existing.Description = robot.Description
	}
	if robot.PersonnelDesign != "" {
		existing.PersonnelDesign = robot.PersonnelDesign
	}
	if robot.ReplyLogic != "" {
		existing.ReplyLogic = robot.ReplyLogic
	}
	if robot.KnowledgeConfig != "" {
		existing.KnowledgeConfig = robot.KnowledgeConfig
	}
	existing.UpdateTime = time.Now()
	robotStorage[robot.ID] = existing

	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "更新成功"})
}

func deleteRobot(c *gin.Context) {
	var req struct {
		ID int `json:"id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"code": 400, "msg": "参数绑定失败"})
		return
	}

	delete(robotStorage, req.ID)
	c.JSON(http.StatusOK, gin.H{"code": 200, "msg": "删除成功"})
}

func listRobots(c *gin.Context) {
	var robots []internetal.Robot
	for _, robot := range robotStorage {
		robots = append(robots, robot)
	}
	c.JSON(http.StatusOK, gin.H{"code": 200, "data": robots})
}
