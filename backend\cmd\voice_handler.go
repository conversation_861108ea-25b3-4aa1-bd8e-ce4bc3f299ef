package main

import (
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
)

type VoiceSettings struct {
	Language     string  `json:"language"`
	Voice        string  `json:"voice"`
	VoicePackage string  `json:"voicePackage"`
	Emotion      string  `json:"emotion"` // 新增情感参数
	Rate         float64 `json:"rate"`
	Volume       int     `json:"volume"`
}

// TTSTestRequest TTS测试请求结构体
type TTSTestRequest struct {
	Text     string  `json:"text"`
	Language string  `json:"language"`
	Voice    string  `json:"voice"`
	Emotion  string  `json:"emotion"` // 新增情感参数
	Rate     float64 `json:"rate"`
	Volume   int     `json:"volume"`
}

// TTSTestResponse TTS测试响应结构体
type TTSTestResponse struct {
	AudioURL string `json:"audio_url"`
}

// 语音包信息结构体
type VoicePackage struct {
	Name     string    `json:"name"`
	FilePath string    `json:"file_path"`
	UploadAt time.Time `json:"upload_at"`
}

// 语音预览历史记录结构体
type VoicePreviewHistory struct {
	ID        int           `json:"id"`
	Settings  VoiceSettings `json:"settings"`
	Timestamp time.Time     `json:"timestamp"`
}

// 全局语音设置
var globalVoiceSettings = VoiceSettings{
	Language: "zh-cn",
	Voice:    "601002", // 前端使用的voice ID
	Emotion:  "neutral",
	Rate:     1.0,
	Volume:   5,
}

// 语音包存储目录
var voicePackageDir = "voice_packages"

// 语音包列表
var voicePackages = make([]VoicePackage, 0)

// 语音预览历史
var voicePreviewHistories = make([]VoicePreviewHistory, 0)

// 初始化语音包目录
func init() {
	if _, err := os.Stat(voicePackageDir); os.IsNotExist(err) {
		os.Mkdir(voicePackageDir, 0755)
	}
}

// handleVoiceSettings 处理语音设置更新
func handleVoiceSettings(c *gin.Context) {
	var settings VoiceSettings
	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
		})
		return
	}

	// 验证参数，允许voice为空但voicePackage不为空
	if settings.Language == "" || (settings.Voice == "" && settings.VoicePackage == "") {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "语言和声音不能为空",
		})
		return
	}

	// 更新全局设置
	globalVoiceSettings = settings

	// 打印日志确认设置已更新
	log.Printf("语音设置已更新: Language=%s, Voice=%s, Emotion=%s, Rate=%.1f, Volume=%d",
		settings.Language, settings.Voice, settings.Emotion, settings.Rate, settings.Volume)

	// 转换并打印腾讯云VoiceType
	tencentVoiceType := getTencentVoiceType(settings.Language, settings.Voice)
	log.Printf("转换后的腾讯云VoiceType: %s", tencentVoiceType)

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "语音设置更新成功",
		"data": settings,
	})
}

// handleGetVoiceSettings 获取当前语音设置
func handleGetVoiceSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": globalVoiceSettings,
	})
}

// handleTTSTest 处理TTS测试
func handleTTSTest(c *gin.Context) {
	var request TTSTestRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "参数错误: " + err.Error(),
		})
		return
	}

	// 调用腾讯云TTS API生成真实音频
	audioURL, err := generateTTSAudio(request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "TTS生成失败: " + err.Error(),
		})
		return
	}

	// 记录语音预览历史
	recordVoicePreviewHistory(request)

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "TTS测试成功",
		"data": TTSTestResponse{
			AudioURL: audioURL,
		},
	})
}

// generateTTSAudio 调用腾讯云TTS API生成音频
func generateTTSAudio(request TTSTestRequest) (string, error) {
	// 这里应该调用腾讯云TTS API，支持情感参数和语音包
	// 目前先返回一个可用的测试音频URL，模拟情感和语音包支持

	// 根据情感调整文本（示例）
	text := request.Text
	if request.Emotion != "" && request.Emotion != "neutral" {
		text = "[" + request.Emotion + "] " + text
	}

	// 临时使用一个在线TTS服务作为测试
	testAudioURL := "https://translate.google.com/translate_tts?ie=UTF-8&tl=" +
		getLanguageCode(request.Language) + "&q=" + text + "&client=tw-ob"

	return testAudioURL, nil
}

// getLanguageCode 将语言代码转换为Google TTS支持的格式
func getLanguageCode(lang string) string {
	switch lang {
	case "zh-cn":
		return "zh"
	case "en-us":
		return "en"
	case "ja-jp":
		return "ja"
	case "ko-kr":
		return "ko"
	case "fr-fr":
		return "fr"
	case "de-de":
		return "de"
	default:
		return "zh"
	}
}

// 上传语音包接口
func handleUploadVoicePackage(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "上传文件失败: " + err.Error(),
		})
		return
	}

	// 保存文件到语音包目录
	filename := filepath.Base(file.Filename)
	savePath := filepath.Join(voicePackageDir, filename)

	if err := c.SaveUploadedFile(file, savePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "保存文件失败: " + err.Error(),
		})
		return
	}

	// 添加到语音包列表
	voicePackages = append(voicePackages, VoicePackage{
		Name:     filename,
		FilePath: savePath,
		UploadAt: time.Now(),
	})

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "上传成功",
		"data": filename,
	})
}

// 获取语音包列表接口
func handleListVoicePackages(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": voicePackages,
	})
}

// 下载语音包接口
func handleDownloadVoicePackage(c *gin.Context) {
	filename := c.Query("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "缺少文件名参数",
		})
		return
	}

	filepath := filepath.Join(voicePackageDir, filename)
	if _, err := os.Stat(filepath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "文件不存在",
		})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/octet-stream")
	c.File(filepath)
}

// 记录语音预览历史
func recordVoicePreviewHistory(request TTSTestRequest) {
	history := VoicePreviewHistory{
		ID: len(voicePreviewHistories) + 1,
		Settings: VoiceSettings{
			Language: request.Language,
			Voice:    request.Voice,
			Emotion:  request.Emotion,
			Rate:     request.Rate,
			Volume:   request.Volume,
		},
		Timestamp: time.Now(),
	}
	voicePreviewHistories = append(voicePreviewHistories, history)
}
