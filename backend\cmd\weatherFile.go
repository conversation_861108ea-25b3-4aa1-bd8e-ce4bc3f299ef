package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// 返回信息结构体
type WeatherNowResponse struct {
	Code       string `json:"code"`
	UpdateTime string `json:"updateTime"`
	FxLink     string `json:"fxLink"`
	Now        struct {
		ObsTime   string `json:"obsTime"`
		Temp      string `json:"temp"`
		Text      string `json:"text"`
		WindDir   string `json:"windDir"`
		WindScale string `json:"windScale"`
		WindSpeed string `json:"windSpeed"`
		Humidity  string `json:"humidity"`
		Precip    string `json:"precip"`
		Pressure  string `json:"pressure"`
		Vis       string `json:"vis"`
		Cloud     string `json:"cloud"`
		Dew       string `json:"dew"`
	} `json:"now"`
	Refer struct {
		Sources []string `json:"sources"`
		License []string `json:"license"`
	} `json:"refer"`
}

type WeatherInfo struct {
	Location    string `json:"location"`
	Code        string `json:"code"`
	Temperature string `json:"temperature"`
	Condition   string `json:"condition"`
	WindDir     string `json:"wind_dir"`
	WindScale   string `json:"wind_scale"`
	WindSpeed   string `json:"wind_speed"`
	Humidity    string `json:"humidity"`
	Precip      string `json:"precip"`
	UpdateTime  string `json:"update_time"`
}

var apiId string = "264e738d45eb46338c314090a94a5a5b"

func GetNowWeather(location string) string {
	baseURL := "https://pj2tundcpt.re.qweatherapi.com/v7/weather/now"

	// 构造查询参数
	query := fmt.Sprintf("location=%s&key=%s&lang=zh&unit=m",
		url.QueryEscape(FindCityIDInInput(location)),
		url.QueryEscape(apiId),
	)
	fullURL := baseURL + "?" + query

	resp, err := http.Get(fullURL)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return ""
	}

	// 解析 JSON 数据
	var weatherResp WeatherNowResponse
	err = json.Unmarshal(body, &weatherResp)

	// 封装为简化结构体
	weatherInfo := WeatherInfo{
		Location:    location,
		Code:        weatherResp.Code,
		Temperature: weatherResp.Now.Temp,
		Condition:   weatherResp.Now.Text,
		WindDir:     weatherResp.Now.WindDir,
		WindScale:   weatherResp.Now.WindScale,
		WindSpeed:   weatherResp.Now.WindSpeed,
		Humidity:    weatherResp.Now.Humidity,
		Precip:      weatherResp.Now.Precip,
		UpdateTime:  weatherResp.UpdateTime,
	}

	jsonData, err := json.Marshal(weatherInfo)
	if err != nil {
		return ""
	}

	return string(jsonData)
}

// 解析拿到的天气数据

func extractCityFromText(text string) string {
	// 简单实现：提取"天气"前面的2-4个字符作为城市名
	// 更复杂的实现可以使用NLP技术

	idx := strings.Index(text, "天气")
	if idx == -1 {
		return ""
	}

	// 取"天气"前面的2-4个字符
	start := idx - 3
	if start < 0 {
		start = 0
	}

	return text[start:idx]
}
func FormatWeatherResponse(city string, data []byte) string {
	var weather WeatherInfo
	err := json.Unmarshal(data, &weather)
	if err != nil {
		return "解析失败"
	}

	if weather.Code != "200" {
		return "获取天气信息失败，请稍后再试。"
	}
	const inputLayout = "2006-01-02T15:04+08:00"
	t, _ := time.Parse(inputLayout, weather.UpdateTime)
	const outputLayout = "2006-01-02 15:04:05"
	// 格式化输出
	formattedTime := t.Format(outputLayout)
	return fmt.Sprintf(
		"%s当前天气：%s，气温%s℃，%s%s级，风速%s km/h，湿度%s%%，降水%s mm。更新时间：%s。",
		city,
		weather.Condition,
		weather.Temperature,
		weather.WindDir,
		weather.WindScale,
		weather.WindSpeed,
		weather.Humidity,
		weather.Precip,
		formattedTime,
	)
}

// 城市Id映射表
func FindCityIDInInput(input string) string {

	cityMap := map[string]string{
		"北京":  "101010100",
		"上海":  "101020100",
		"广州":  "101280601",
		"深圳":  "101280701",
		"成都":  "101270101",
		"重庆":  "101040100",
		"杭州":  "101210101",
		"武汉":  "101200101",
		"西安":  "101110101",
		"南京":  "101190101",
		"沈阳":  "101070101",
		"大连":  "101070201",
		"青岛":  "101120201",
		"苏州":  "101190401",
		"长沙":  "101250101",
		"厦门":  "101230201",
		"哈尔滨": "101050101",
		"长春":  "101060101",
		"昆明":  "101290101",
		"贵阳":  "101260101",
	}

	inputLower := strings.ToLower(input)
	// 匹配城市id
	for name, id := range cityMap {
		if strings.Contains(inputLower, strings.ToLower(name)) {
			return id // 返回对应的城市ID
		}
	}

	return "" // 没有找到任何匹配的城市ID
}
