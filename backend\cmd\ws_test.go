package main

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
)

func TestWebSocketConnection(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c, err := upgrader.Upgrade(w, r, nil)
		assert.NoError(t, err)
		defer c.Close()
	}))
	defer server.Close()

	u := "ws" + server.URL[len("http"):] // 把 http:// 替换成 ws://

	ws, _, err := websocket.DefaultDialer.Dial(u, nil)
	assert.NoError(t, err)
	defer ws.Close()

	// 发送一个 offer
	msg := WebRTCMessage{
		Type: "offer",
		SDP:  "fake-sdp-data",
	}
	err = ws.WriteJSON(msg)
	assert.NoError(t, err)
}
