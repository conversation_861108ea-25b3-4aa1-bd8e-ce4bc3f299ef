package internetal

import (
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// 数据库配置常量
const (
	dbUser     = "root"
	dbPassword = "123456"
	dbHost     = "localhost"
	dbPort     = "3306"
	dbName     = "myapp_db"
)

// Assistant 数据模型
type Assistant struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	Name            string    `gorm:"size:100;not null" json:"name"`
	Description     string    `gorm:"size:500" json:"description"`
	PersonnelDesign string    `gorm:"type:text" json:"personnel_design"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// API请求/响应结构体
type CreateRequest struct {
	Name            string `json:"name" binding:"required"`
	Description     string `json:"description"`
	PersonnelDesign string `json:"personnel_design"`
}

type UpdateRequest struct {
	ID              uint   `json:"id" binding:"required"`
	Name            string `json:"name"`
	Description     string `json:"description"`
	PersonnelDesign string `json:"personnel_design"`
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

var db *gorm.DB

func main() {
	// 初始化数据库连接
	initDB()

	// 设置Gin路由
	r := gin.Default()

	// 注册API路由
	api := r.Group("/api/robot")
	{
		api.POST("/create", handleCreate)
		api.POST("/update", handleUpdate)
		api.POST("/delete/:id", handleDelete)
		api.GET("/list", handleList)
	}

	// 启动服务器
	log.Printf("Server started on :8080 (MySQL: %s@%s:%s/%s)", dbUser, dbHost, dbPort, dbName)
	if err := r.Run(":8080"); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

// 初始化MySQL数据库连接
func initDB() {
	dsn := dbUser + ":" + dbPassword + "@tcp(" + dbHost + ":" + dbPort + ")/" + dbName + "?charset=utf8mb4&parseTime=True&loc=Local"
	var err error
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to MySQL:", err)
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(&Assistant{}); err != nil {
		log.Fatal("Failed to migrate tables:", err)
	}

	// 测试连接
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal("Failed to get DB instance:", err)
	}
	if err := sqlDB.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	// 连接池配置
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Successfully connected to MySQL database")
}

// API处理器函数
func handleCreate(c *gin.Context) {
	var req CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		sendError(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	assistant := Assistant{
		Name:            req.Name,
		Description:     req.Description,
		PersonnelDesign: req.PersonnelDesign,
	}

	if err := db.Create(&assistant).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to create assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant created successfully", assistant)
}

func handleUpdate(c *gin.Context) {
	var req UpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		sendError(c, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	var assistant Assistant
	if err := db.First(&assistant, req.ID).Error; err != nil {
		sendError(c, http.StatusNotFound, "Assistant not found")
		return
	}

	// 使用Select只更新非零值字段
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.PersonnelDesign != "" {
		updates["personnel_design"] = req.PersonnelDesign
	}

	if err := db.Model(&assistant).Select("*").Updates(updates).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to update assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant updated successfully", assistant)
}

func handleDelete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		sendError(c, http.StatusBadRequest, "Missing ID parameter")
		return
	}

	if err := db.Delete(&Assistant{}, id).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to delete assistant: "+err.Error())
		return
	}

	sendSuccess(c, "Assistant deleted successfully", nil)
}

func handleList(c *gin.Context) {
	var assistants []Assistant
	if err := db.Order("created_at DESC").Find(&assistants).Error; err != nil {
		sendError(c, http.StatusInternalServerError, "Failed to fetch assistants: "+err.Error())
		return
	}

	sendSuccess(c, "Assistants fetched successfully", assistants)
}

// 辅助函数
func sendError(c *gin.Context, code int, message string) {
	c.JSON(code, Response{
		Code:    code,
		Message: message,
	})
}

func sendSuccess(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	})
}
