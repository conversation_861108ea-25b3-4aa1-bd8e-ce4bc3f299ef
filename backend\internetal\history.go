package internetal

import (
    "time"
)

// ConversationHistory 对话历史记录
type ConversationHistory struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    AssistantID  int       `json:"assistant_id"`
    AssistantName string   `json:"assistant_name"`
    FirstMessage string    `json:"first_message"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}

// ConversationMessage 对话消息
type ConversationMessage struct {
    ID             uint      `json:"id" gorm:"primaryKey"`
    ConversationID uint      `json:"conversation_id"`
    Content        string    `json:"content"`
    IsUser         bool      `json:"is_user"`
    MessageType    string    `json:"message_type" gorm:"default:'text'"` // 'text' 或 'voice'
    CreatedAt      time.Time `json:"created_at"`
}
