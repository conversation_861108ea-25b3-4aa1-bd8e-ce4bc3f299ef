package internetal

import "time"

type Robot struct {
	ID              int       `gorm:"primaryKey" json:"id"`
	Name            string    `gorm:"column:name" json:"name"`
	Description     string    `gorm:"column:description" json:"description"`
	PersonnelDesign string    `gorm:"column:personnel_design" json:"personnel_design"`
	CreateTime      time.Time `gorm:"column:create_time" json:"create_time"`
	UpdateTime      time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`
	ReplyLogic      string    `gorm:"column:reply_logic" json:"reply_logic"`
	KnowledgeConfig string    `gorm:"column:knowledge_config" json:"knowledge_config"`
	
}

// 知识库配置结构
type KnowledgeConfig struct {
	CallMethod     string  `json:"call_method"`     // 调用方式: auto/manual
	SearchStrategy string  `json:"search_strategy"` // 搜索策略: mixed/semantic/full_text
	MaxRecall      int     `json:"max_recall"`      // 最大召回数量
	MinScore       float64 `json:"min_score"`       // 最小匹配度
	QueryRewrite   bool    `json:"query_rewrite"`   // 查询改写
	ResultRerank   bool    `json:"result_rerank"`   // 结果重排
}

// 回复逻辑配置结构
type ReplyLogicConfig struct {
	SystemRole    string  `json:"system_role"`    // 系统角色
	DialogCommand string  `json:"dialog_command"` // 对话指令
	ResponseMode  string  `json:"response_mode"`  // 回复模式
	Temperature   float64 `json:"temperature"`    // 温度
	MaxTokens     int     `json:"max_tokens"`     // 最大token数
	Language      string  `json:"language"`       // 语言
	Speaker       string  `json:"speaker"`        // 发音人
	SpeechSpeed   float64 `json:"speech_speed"`   // 语速
}

