package utils

import (
	"github.com/goccy/go-json"
	"sync"
)

type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

type FunctionCall struct {
	Name      string          `json:"name"`
	Arguments json.RawMessage `json:"arguments"`
}

type FunctionDefinition struct {
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Parameters  json.RawMessage `json:"parameters"`
}

// 实现多轮对话，保存交流数据
type Message struct {
	Role      string     `json:"role"`
	Content   string     `json:"content"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

type MessageStore struct {
	MessageStore []Message `json:"message"`
}

type SessionStore struct {
	MessageStore map[string]*MessageStore
	mu           sync.Mutex
}

func NewSessionStore() *SessionStore {
	return &SessionStore{
		MessageStore: make(map[string]*MessageStore),
	}
}

var sessionStore *SessionStore = NewSessionStore()

func GetSessionStore() *SessionStore {
	return sessionStore
}

func (st *SessionStore) GetMessageStore(assistantName string) *MessageStore {
	st.mu.Lock()
	defer st.mu.Unlock()

	_, exists := st.MessageStore[assistantName]
	if !exists {
		st.MessageStore[assistantName] = &MessageStore{
			MessageStore: make([]Message, 0),
		}
	}
	return st.MessageStore[assistantName]
}

func (st *SessionStore) AddMessage(assistantName string, message Message) {
	store := st.GetMessageStore(assistantName)
	store.MessageStore = append(store.MessageStore, message)
	if len(store.MessageStore) > 21 {
		if store.MessageStore[0].Role == "system" {
			store.MessageStore = append(store.MessageStore[:1], store.MessageStore[3:]...)
		}
		if store.MessageStore[1].Role == "user" {
			store.MessageStore = store.MessageStore[2:]
		}
	}
}

func (st *SessionStore) DeleteMessageStore(assistantName string) {
	delete(st.MessageStore, assistantName)
}

func (st *SessionStore) ClearAllSessions() {
	st.mu.Lock()
	defer st.mu.Unlock()
	for k := range st.MessageStore {
		delete(st.MessageStore, k)
	}
}
