<template>
  <div class="flex h-screen bg-gradient-to-br from-gray-50 to-gray-100 font-serif">
    <!-- 左侧助手管理 -->
    <AssistantList
      :assistants="assistants"
      :current-selected-assistant="currentSelectedAssistant"
      :search-query="searchQuery"
      @add="openAddModal"
      @search="searchQuery = $event"
      @select="selectAssistant"
      @edit="openEditModal"
      @settings="openSettingsModal"
      @delete="openDeleteModal"
      @history="showHistoryModal = true"
    />

    <!-- 右侧对话展示 -->
    <ChatArea
      ref="chatAreaRef"
      :current-selected-assistant="currentSelectedAssistant"
      :messages="currentMessages"
      :message-input="messageInput"
      @reset="resetChat"
      @input="messageInput = $event"
      @send="sendMessage"
      @dial="showDialModal = true"
      @voice-settings="showVoiceSettingsModal = true"
      @add-message="addMessage"
    />

    <!-- 弹窗组件 -->
    <ModalComponents
      :show-add-modal="showAddModal"
      :show-settings-modal="showSettingsModal"
      :show-delete-modal="showDeleteModal"
      :show-dial-modal="showDialModal"
      :is-editing="isEditing"
      :current-assistant="currentAssistant"
      :current-settings-assistant="currentSettingsAssistant"
      :current-personality="currentPersonality"
      :delete-assistant-name="deleteAssistantName"
      :dial-number="dialNumber"
      @close-add="closeModal"
      @update-name="currentAssistant.name = $event"
      @update-description="currentAssistant.description = $event"
      @save="saveAssistant"
      @close-settings="closeSettingsModal"
      @update-personality="currentPersonality = $event"
      @save-personality="savePersonality"
      @close-delete="closeDeleteModal"
      @confirm-delete="confirmDelete"
      @close-dial="showDialModal = false"
      @add-number="addNumber"
      @make-call="makeCall"
      @clear-number="clearNumber"
    />
    
    <HistoryModal
      :show="showHistoryModal"
      :assistants="assistants"
      @close="showHistoryModal = false"
      @restore-conversation="restoreConversation"
    />
    
    <VoiceSettingsModal
      :show="showVoiceSettingsModal"
      :current-settings="voiceSettings"
      @close="showVoiceSettingsModal = false"
      @settings-changed="handleVoiceSettingsChanged"
    />
  </div>
</template>

<script>
import AssistantList from './components/AssistantList.vue'
import ChatArea from './components/ChatArea.vue'
import ModalComponents from './components/ModalComponents.vue'
import HistoryModal from './components/HistoryModal.vue'
import VoiceSettingsModal from './components/VoiceSettingsModal.vue'

export default {
  name: 'App',
  components: {
    AssistantList,
    ChatArea,
    ModalComponents,
    HistoryModal,
    VoiceSettingsModal
  },
  data() {
    return {
      // 弹窗状态
      showAddModal: false,
      showDialModal: false,
      showSettingsModal: false,
      showDeleteModal: false,
      showHistoryModal: false,
      showVoiceSettingsModal: false,
      
      // 对话相关
      currentConversationId: null,
      dialNumber: '',
      messageInput: '',
      searchQuery: '',
      isEditing: false,
      
      // 助手相关
      currentSelectedAssistant: null,
      currentSettingsAssistant: null,
      currentPersonality: '',
      deleteAssistantId: null,
      deleteAssistantName: '',
      currentAssistant: {
        id: null,
        name: '',
        description: ''
      },
      
      // 数据存储
      assistantMessages: {},
      assistantPersonalities: {},
      
      // 语音设置
      voiceSettings: {
        language: 'zh-cn',
        voice: '601002',
        emotion: 'neutral',
        rate: 1.0,
        volume: 5,
        voicePackage: ''
      },
      
      // 默认助手
      assistants: [
        {
          id: 1,
          name: '默认机器人',
          description: '这是一个可以用于简单测试对话的语音机器人。'
        }
      ]
    }
  },
  computed: {
    currentMessages() {
      if (!this.currentSelectedAssistant) return []
      return this.assistantMessages[this.currentSelectedAssistant.id] || []
    }
  },
  methods: {
    // 助手管理
    openAddModal() {
      this.isEditing = false
      this.currentAssistant = {
        id: null,
        name: '',
        description: ''
      }
      this.showAddModal = true
    },
    
    openEditModal(assistant) {
      this.isEditing = true
      this.currentAssistant = {
        id: assistant.id,
        name: assistant.name,
        description: assistant.description
      }
      this.showAddModal = true
    },
    
    closeModal() {
      this.showAddModal = false
      this.currentAssistant = {
        id: null,
        name: '',
        description: ''
      }
    },
    
    async saveAssistant() {
      if (this.currentAssistant.name.trim() && this.currentAssistant.description.trim()) {
        if (this.isEditing) {
          // 编辑机器人
          try {
            const response = await fetch('http://localhost:8080/api/robot/update', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                id: this.currentAssistant.id,
                name: this.currentAssistant.name.trim(),
                description: this.currentAssistant.description.trim(),
                personnel_design: this.assistantPersonalities[this.currentAssistant.id] || "你是一个友善、专业的AI助手。"
              })
            })

            const result = await response.json()
            if (result.code === 200) {
              const index = this.assistants.findIndex(a => a.id === this.currentAssistant.id)
              if (index !== -1) {
                this.assistants[index] = { ...this.currentAssistant }
                if (this.currentSelectedAssistant?.id === this.currentAssistant.id) {
                  this.currentSelectedAssistant = { ...this.currentAssistant }
                }
              }
            } else {
              console.error('更新失败:', result.msg)
            }
          } catch (error) {
            console.error('网络错误:', error)
          }
        } else {
          // 创建新机器人
          const robotData = {
            name: this.currentAssistant.name.trim(),
            description: this.currentAssistant.description.trim(),
            personnel_design: this.assistantPersonalities[this.currentAssistant.id] || "你是一个友善、专业的AI助手。"
          }

          try {
            const response = await fetch('http://localhost:8080/api/robot/create', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(robotData)
            })

            const result = await response.json()
            if (result.code === 200) {
              await this.fetchRobots()

              // 自动选中新建的机器人
              const newId = result.data?.id
              const newAssistant = this.assistants.find(a => a.id === newId)
              if (newAssistant) {
                this.selectAssistant(newAssistant)
              } else {
                this.selectAssistant(this.assistants[this.assistants.length - 1])
              }
            } else {
              console.error('创建失败:', result.msg)
            }
          } catch (error) {
            console.error('网络错误:', error)
          }
        }

        this.closeModal()
      }
    },
    
    // 人设配置
    openSettingsModal(assistant) {
      this.currentSettingsAssistant = assistant
      this.currentPersonality = this.assistantPersonalities[assistant.id] || '你是一个友善、专业的AI助手。'
      this.showSettingsModal = true
    },
    
    closeSettingsModal() {
      this.showSettingsModal = false
      this.currentSettingsAssistant = null
      this.currentPersonality = ''
    },
    
    async savePersonality() {
      if (this.currentSettingsAssistant) {
        try {
          const response = await fetch('http://localhost:8080/api/robot/update', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: this.currentSettingsAssistant.id,
              name: this.currentSettingsAssistant.name,
              description: this.currentSettingsAssistant.description,
              personnel_design: this.currentPersonality
            })
          })

          const result = await response.json()
          if (result.code === 200) {
            this.assistantPersonalities[this.currentSettingsAssistant.id] = this.currentPersonality
            console.log('人设配置保存成功')
          } else {
            console.error('人设配置保存失败:', result.msg)
            alert('保存失败: ' + result.msg)
          }
        } catch (error) {
          console.error('网络错误:', error)
          alert('网络错误，保存失败')
        }

        this.closeSettingsModal()
      }
    },
    
    // 删除助手
    openDeleteModal(assistant) {
      this.deleteAssistantId = assistant.id
      this.deleteAssistantName = assistant.name
      this.showDeleteModal = true
    },
    
    closeDeleteModal() {
      this.showDeleteModal = false
      this.deleteAssistantId = null
      this.deleteAssistantName = ''
    },
    
    async confirmDelete() {
      if (this.deleteAssistantId) {
        try {
          const response = await fetch('http://localhost:8080/api/robot/delete', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              id: this.deleteAssistantId
            })
          })

          const result = await response.json()
          if (result.code === 200) {
            this.assistants = this.assistants.filter(assistant => assistant.id !== this.deleteAssistantId)
            if (this.currentSelectedAssistant?.id === this.deleteAssistantId) {
              this.currentSelectedAssistant = null
            }
            delete this.assistantMessages[this.deleteAssistantId]
            delete this.assistantPersonalities[this.deleteAssistantId]
          } else {
            console.error('删除失败:', result.msg)
          }
        } catch (error) {
          console.error('网络错误:', error)
        }

        this.closeDeleteModal()
      }
    },
    
    // 拨号功能
    addNumber(number) {
      this.dialNumber += number
    },
    
    clearNumber() {
      this.dialNumber = ''
    },
    
    makeCall() {
      if (this.dialNumber) {
        alert(`拨打电话: ${this.dialNumber}`)
        this.dialNumber = ''
        this.showDialModal = false
      }
    },
    
    // 对话功能
    selectAssistant(assistant) {
      this.currentSelectedAssistant = assistant
      if (!this.assistantMessages[assistant.id]) {
        this.assistantMessages[assistant.id] = [
          {
            id: Date.now(),
            content: `您好！我是${assistant.name}，我是由豆包大模型驱动的AI助手，有什么可以帮助您的吗？`,
            isUser: false,
            timestamp: new Date()
          }
        ]
      }
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    async sendMessage() {
      if (this.messageInput.trim() && this.currentSelectedAssistant) {
        // 添加用户消息到界面
        this.addMessage({
          id: Date.now(),
          content: this.messageInput.trim(),
          isUser: true,
          timestamp: new Date()
        })

        const userMessage = this.messageInput.trim()
        this.messageInput = ''

        try {
          // 发送人设配置（如果有）
          const personality = this.assistantPersonalities[this.currentSelectedAssistant.id]
          if (personality && personality.trim()) {
            await fetch('http://localhost:8080/api/text/send', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                user_id: this.currentSelectedAssistant.id.toString(),
                message: {
                  role: "system",
                  content: personality
                }
              })
            })
          }

          // 发送用户消息
          const response = await fetch('http://localhost:8080/api/text/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: this.currentSelectedAssistant.id.toString(),
              message: {
                role: "user",
                content: userMessage
              }
            })
          })

          const result = await response.json()

          if (result.code === 200) {
            // 添加AI回复
            this.addMessage({
              id: Date.now() + 1,
              content: result.data,
              isUser: false,
              timestamp: new Date()
            })
          } else {
            throw new Error(result.msg || '服务器返回错误')
          }
        } catch (error) {
          console.error('发送消息失败:', error)
          this.addMessage({
            id: Date.now() + 1,
            content: '抱歉，AI服务暂时不可用，请稍后重试',
            isUser: false,
            timestamp: new Date()
          })
        }
      }
    },
    
    // 修复后的滚动到底部方法
    scrollToBottom() {
      this.$nextTick(() => {
        // 使用ref直接获取组件
        const chatArea = this.$refs.chatAreaRef
        if (chatArea && chatArea.$refs.chatContainer) {
          const container = chatArea.$refs.chatContainer
          container.scrollTop = container.scrollHeight
        }
      })
    },
    
    resetChat() {
      if (this.currentSelectedAssistant) {
        this.assistantMessages[this.currentSelectedAssistant.id] = [
          {
            id: Date.now(),
            content: `您好！我是${this.currentSelectedAssistant.name}，我是由豆包大模型驱动的AI助手，有什么可以帮助您的吗？`,
            isUser: false,
            timestamp: new Date()
          }
        ]
        this.messageInput = ''
        this.scrollToBottom()
      }
    },
    
    // 获取机器人列表
    async fetchRobots() {
      try {
        const response = await fetch('http://localhost:8080/api/robot/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        const result = await response.json()

        if (result.code === 200 && result.data && result.data.length > 0) {
          this.assistants = result.data.map(robot => ({
            id: robot.id,
            name: robot.name,
            description: robot.description
          }))

          // 加载人设配置
          result.data.forEach(robot => {
            if (robot.personnel_design) {
              this.assistantPersonalities[robot.id] = robot.personnel_design
            }
          })
        } else {
          console.log('后端无数据，使用默认机器人')
        }
      } catch (error) {
        console.error('获取机器人列表失败:', error)
      }
    },
    
    // 保存对话
    async saveConversation() {
      if (!this.currentSelectedAssistant || !this.currentMessages.length) return

      try {
        const response = await fetch('http://localhost:8080/api/history/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            assistant_id: this.currentSelectedAssistant.id,
            assistant_name: this.currentSelectedAssistant.name,
            messages: this.currentMessages,
            conversation_id: this.currentConversationId
          })
        })
        const result = await response.json()
        if (result.code === 200) {
          this.currentConversationId = result.data.conversation_id
        }
      } catch (error) {
        console.error('保存对话失败:', error)
      }
    },
    
    // 恢复对话
    restoreConversation(data) {
      const assistant = this.assistants.find(a => a.id === data.assistant.id)
      if (assistant) {
        this.currentSelectedAssistant = assistant
        this.assistantMessages[assistant.id] = data.messages.map(msg => ({
          id: msg.id,
          content: msg.content,
          isUser: msg.is_user,
          timestamp: new Date(msg.created_at)
        }))
        this.scrollToBottom()
      }
    },
    
    // 添加消息
    addMessage(message) {
      if (this.currentSelectedAssistant) {
        if (!this.assistantMessages[this.currentSelectedAssistant.id]) {
          this.assistantMessages[this.currentSelectedAssistant.id] = []
        }
        this.assistantMessages[this.currentSelectedAssistant.id].push(message)
        this.scrollToBottom()
      }
    },
    
    // 语音设置
    async fetchVoiceSettings() {
      try {
        const response = await fetch('http://localhost:8080/api/voice/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        
        if (response.ok) {
          const result = await response.json()
          if (result.code === 200 && result.data) {
            this.voiceSettings = result.data
            console.log('获取到的语音设置:', result.data)
          }
        }
      } catch (error) {
        console.error('获取语音设置失败:', error)
      }
    },
    
    // 处理语音设置变更
    handleVoiceSettingsChanged(settings) {
      this.voiceSettings = settings
      console.log('语音设置已更新:', settings)
    }
  },
  
  mounted() {
    // 初始化时获取机器人列表和语音设置
    this.fetchRobots()
    this.fetchVoiceSettings()
  }
}
</script>
    