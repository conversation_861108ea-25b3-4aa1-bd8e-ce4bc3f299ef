<template>
  <div 
    @click="$emit('select', assistant)"
    class="bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl p-5 border border-gray-200/60 hover:shadow-xl transition-all duration-300 hover:border-blue-300/60 group cursor-pointer relative overflow-hidden backdrop-blur-sm"
    :class="isSelected ? 'border-blue-400/80 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 shadow-lg ring-2 ring-blue-200/50' : ''"
  >
    <!-- 装饰性背景元素 -->
    <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100/40 to-transparent rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-300"></div>
    <div class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-indigo-100/30 to-transparent rounded-full translate-y-8 -translate-x-8 group-hover:scale-110 transition-transform duration-300"></div>
    
    <div class="flex items-start justify-between mb-3 relative z-10">
      <h3 class="font-semibold text-gray-900 text-base group-hover:text-blue-700 transition-colors font-serif">{{ assistant.name }}</h3>
      <div class="w-3 h-3 rounded-full shadow-sm relative" :class="isSelected ? 'bg-blue-500' : 'bg-emerald-400'">
        <div class="absolute inset-0 rounded-full animate-pulse" :class="isSelected ? 'bg-blue-400/50' : 'bg-emerald-300/50'"></div>
      </div>
    </div>
    <p class="text-gray-600 text-sm mb-4 leading-relaxed font-serif line-clamp-2 relative z-10">{{ assistant.description }}</p>
    <div class="flex items-center justify-between relative z-10">
      <button 
        @click.stop="$emit('edit', assistant)"
        class="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-blue-50/50 px-2 py-1 rounded-lg font-serif"
      >
        <svg class="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
        </svg>
        <span>编辑</span>
      </button>
      <button 
        @click.stop="$emit('settings', assistant)"
        class="text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-purple-50/50 px-2 py-1 rounded-lg font-serif"
      >
        <svg class="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
        </svg>
        <span>设置</span>
      </button>
      <button 
        @click.stop="$emit('delete', assistant)"
        class="text-red-600 hover:text-red-700 text-sm font-medium transition-colors flex items-center space-x-1 hover:bg-red-50/50 px-2 py-1 rounded-lg font-serif"
      >
        <svg class="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
        </svg>
        <span>删除</span>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssistantCard',
  props: {
    assistant: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select', 'edit', 'settings', 'delete']
}
</script>
