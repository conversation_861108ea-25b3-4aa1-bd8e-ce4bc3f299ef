<template>
  <div class="w-80 bg-white shadow-xl border-r border-gray-100 flex flex-col">
    <div class="p-6 flex-shrink-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2 flex items-center font-sans">
          <span class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-sans">🤖</span>
          智能语音机器人
        </h1>
        <p class="text-gray-500 text-sm leading-relaxed font-sans">提供一站式的智能语音解决方案</p>
      </div>

      <!-- 按钮区域：新增和搜索并排 -->
      <div class="flex space-x-3 mb-6">
        <button 
          @click="$emit('add')"
          class="w-1/2 bg-gradient-to-r from-blue-500/90 to-cyan-500/90 backdrop-blur-sm border border-white/20 hover:from-blue-600/95 hover:to-cyan-600/95 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-sans"
        >
          新增
        </button>
        
        <div class="relative w-1/2">
          <input 
            :value="searchQuery"
            @input="$emit('search', $event.target.value)"
            type="text" 
            placeholder="搜索助手..."
            class="w-full px-4 py-3 pl-10 border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gradient-to-r from-red-400/80 to-pink-400/80 backdrop-blur-sm focus:from-red-500/90 focus:to-pink-500/90 text-white placeholder-white/70 font-sans"
          >
          <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 font-sans">🔍</span>
        </div>
      </div>

      <!-- 在新增按钮下方添加历史对话按钮 -->
      <button 
        @click="$emit('history')"
        class="w-full bg-gradient-to-r from-yellow-400/80 via-orange-400/80 via-pink-400/80 via-purple-400/80 to-blue-400/80 backdrop-blur-sm border border-white/30 hover:from-yellow-500/90 hover:via-orange-500/90 hover:via-pink-500/90 hover:via-purple-500/90 hover:to-blue-500/90 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-sans mt-3"
      >
        <span class="mr-2 text-lg font-sans">⏳</span>
        历史对话
      </button>
    </div>

    <!-- 助手列表 - 可滚动区域 -->
    <div class="flex-1 overflow-y-auto px-6 pb-6">
      <div class="space-y-3">
        <AssistantCard
          v-for="assistant in filteredAssistants" 
          :key="assistant.id"
          :assistant="assistant"
          :is-selected="currentSelectedAssistant?.id === assistant.id"
          @select="$emit('select', $event)"
          @edit="$emit('edit', $event)"
          @settings="$emit('settings', $event)"
          @delete="$emit('delete', $event)"
        />
        
        <!-- 无搜索结果提示 -->
        <div v-if="filteredAssistants.length === 0 && searchQuery" class="text-center py-6">
          <p class="text-gray-500 font-sans">未找到匹配的助手</p>
          <p class="text-gray-400 text-sm mt-2 font-sans">请尝试其他关键词</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AssistantCard from './AssistantCard.vue'

export default {
  name: 'AssistantList',
  components: {
    AssistantCard
  },
  props: {
    assistants: {
      type: Array,
      required: true
    },
    currentSelectedAssistant: {
      type: Object,
      default: null
    },
    searchQuery: {
      type: String,
      default: ''
    }
  },
  computed: {
    filteredAssistants() {
      if (!this.searchQuery.trim()) {
        return this.assistants
      }
      return this.assistants.filter(assistant => 
        assistant.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        assistant.description.toLowerCase().includes(this.searchQuery.toLowerCase())
      )
    }
  },
  emits: ['add', 'search', 'select', 'edit', 'settings', 'delete', 'history']
}
</script>

