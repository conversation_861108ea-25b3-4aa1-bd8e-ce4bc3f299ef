<template>
  <div class="flex-1 flex flex-col bg-gradient-to-br from-blue-50/30 to-purple-50/30">
    <!-- 顶部标题栏 -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-gray-100 px-8 py-5 flex items-center justify-between">
      <h2 class="text-xl font-serif font-semibold text-gray-900 flex items-center">
        <span class="w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mr-3"></span>
        {{ currentSelectedAssistant ? currentSelectedAssistant.name : '效果测试' }}
      </h2>
      <div class="flex items-center space-x-3">
        <button
          @click="$emit('voice-settings')"
          class="bg-purple-100 hover:bg-purple-200 text-purple-700 px-5 py-2.5 rounded-xl transition-all duration-200 flex items-center shadow-sm hover:shadow-md font-serif"
        >
          <span class="mr-2 font-serif">🎵</span>
          语音设置
        </button>
        <button
          @click="$emit('reset')"
          class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-5 py-2.5 rounded-xl transition-all duration-200 flex items-center shadow-sm hover:shadow-md font-serif"
        >
          <span class="mr-2 font-serif">🔄</span>
          重置对话
        </button>
      </div>
    </div>

    <!-- 对话内容区 -->
    <div ref="chatContainer" class="flex-1 overflow-y-auto p-8 space-y-6">
      <!-- 消息列表 -->
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="flex items-start space-x-4"
        :class="message.isUser ? 'justify-end' : ''"
      >
        <!-- 用户消息 -->
        <template v-if="message.isUser">
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-2xl max-w-xs shadow-lg break-words">
            <p class="text-sm leading-relaxed font-serif">{{ message.content }}</p>
          </div>
          <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center text-sm font-medium shadow-lg flex-shrink-0 font-serif">
            用户
          </div>
        </template>
        
        <!-- 机器人消息 -->
        <template v-else>
          <div class="w-10 h-10 bg-gradient-to-r from-gray-400 to-gray-500 text-white rounded-full flex items-center justify-center text-lg shadow-lg flex-shrink-0 font-serif">
            🤖
          </div>
          <div class="bg-white border border-gray-100 px-4 py-3 rounded-2xl shadow-lg max-w-md break-words">
            <div 
              class="text-gray-800 text-sm leading-relaxed font-serif markdown-content"
              v-html="renderMarkdown(message.content)"
            ></div>
          </div>
        </template>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="bg-white/90 backdrop-blur-sm border-t border-gray-100 p-6">
      <div class="flex items-center space-x-4">
        <input
          :value="messageInput"
          @input="$emit('input', $event.target.value)"
          @keypress.enter="$emit('send')"
          type="text"
          placeholder="输入消息..."
          class="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-serif"
        />
        <VoiceRecorder @voice-message="handleVoiceMessage" />
        <button
          @click="$emit('send')"
          class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-colors font-serif"
        >
          发送
        </button>
        <button
          @click="$emit('dial')"
          class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl transition-colors font-serif"
        >
          拨号
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownIt from 'markdown-it'
import VoiceRecorder from './VoiceRecorder.vue'

const md = new MarkdownIt({
  breaks: true,
  linkify: true
})

export default {
  name: 'ChatArea',
  components: {
    VoiceRecorder
  },
  props: {
    currentSelectedAssistant: {
      type: Object,
      default: null
    },
    messages: {
      type: Array,
      default: () => []
    },
    messageInput: {
      type: String,
      default: ''
    }
  },
  emits: ['reset', 'input', 'send', 'dial', 'add-message', 'voice-settings'],
  
  methods: {
    renderMarkdown(content) {
      return md.render(content)
    },
    
    handleVoiceMessage(message) {
      console.log('ChatArea收到语音消息:', message)
      
      switch (message.type) {
        case 'asr_result':
          console.log('收到ASR识别结果:', message.data)
          // 添加用户消息到对话
          this.$emit('add-message', {
            id: Date.now(),
            content: message.data,
            isUser: true,
            timestamp: new Date()
          })
          break
          
        case 'llm_result':
          console.log('收到LLM回复:', message.data)
          // 添加AI回复到对话
          this.$emit('add-message', {
            id: Date.now() + 1,
            content: message.data,
            isUser: false,
            timestamp: new Date()
          })
          break
          
        case 'tts_result':
          console.log('收到TTS音频URL:', message.data)
          // 播放TTS音频
          this.playAudio(message.data)
          break
          
        case 'error':
          console.error('语音处理错误:', message.data)
          // 显示错误消息
          this.$emit('add-message', {
            id: Date.now(),
            content: `错误: ${message.data}`,
            isUser: false,
            timestamp: new Date()
          })
          break
          
        default:
          console.log('未知消息类型:', message.type)
      }
    },
    
    async playAudio(audioURL) {
      console.log('正在播放音频:', audioURL)
      
      try {
        const audio = new Audio(audioURL)
        
        audio.oncanplaythrough = () => {
          console.log('音频可以完整播放')
          audio.play()
        }
        
        audio.onerror = (error) => {
          console.error('音频播放错误:', error)
        }
        
        audio.onended = () => {
          console.log('音频播放完成')
        }
        
        audio.load()
        
      } catch (error) {
        console.error('音频播放准备失败:', error)
      }
    }
  },
  
  watch: {
    messages: {
      handler() {
        this.$nextTick(() => {
          const container = this.$refs.chatContainer
          if (container) {
            container.scrollTop = container.scrollHeight
          }
        })
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  font-weight: bold;
  margin: 0.5em 0;
}

.markdown-content :deep(h1) {
  font-size: 1.2em;
}

.markdown-content :deep(h2) {
  font-size: 1.1em;
}

.markdown-content :deep(h3) {
  font-size: 1.05em;
}

.markdown-content :deep(p) {
  margin: 0.5em 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content :deep(li) {
  margin: 0.2em 0;
}

.markdown-content :deep(code) {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background-color: #f3f4f6;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin: 0.5em 0;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 0.5em 0;
  color: #6b7280;
}

.markdown-content :deep(strong) {
  font-weight: bold;
}

.markdown-content :deep(em) {
  font-style: italic;
}

.markdown-content :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #f9fafb;
  font-weight: bold;
}
</style>
