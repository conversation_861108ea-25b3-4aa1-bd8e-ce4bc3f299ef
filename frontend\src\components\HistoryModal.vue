<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl w-4/5 h-4/5 max-w-6xl flex">
      <!-- 左侧对话列表 -->
      <div class="w-1/3 border-r border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-xl font-serif font-semibold text-gray-900">历史对话</h3>
          <button @click="$emit('close')" class="text-gray-500 hover:text-gray-700">
            <span class="text-2xl">&times;</span>
          </button>
        </div>
        
        <!-- 助手筛选 -->
        <select 
          v-model="selectedAssistantId" 
          @change="fetchHistoryList"
          class="w-full mb-4 px-3 py-2 border border-gray-300 rounded-lg font-serif"
        >
          <option value="">所有助手</option>
          <option v-for="assistant in assistants" :key="assistant.id" :value="assistant.id">
            {{ assistant.name }}
          </option>
        </select>

        <!-- 对话列表 -->
        <div class="space-y-3 overflow-y-auto max-h-96" style="scroll-behavior: smooth;">
          <div 
            v-for="history in historyList" 
            :key="history.id"
            @click="selectHistory(history)"
            :class="['p-4 rounded-lg cursor-pointer transition-all duration-200 ease-in-out transform hover:scale-[1.02]', 
                     selectedHistory?.id === history.id ? 'bg-blue-100 border-blue-300 shadow-md' : 'bg-gray-50 hover:bg-gray-100 hover:shadow-sm']"
          >
            <div class="font-serif font-medium text-gray-900">{{ history.assistant_name }}</div>
            <div class="text-sm text-gray-600 mt-1">{{ formatDate(history.created_at) }}</div>
            <div class="text-sm text-gray-500 mt-1 truncate">{{ history.first_message }}</div>
          </div>
        </div>
      </div>

      <!-- 右侧对话详情 -->
      <div class="flex-1 p-6 flex flex-col">
        <div v-if="selectedHistory" class="flex-1">
          <h4 class="text-lg font-serif font-semibold mb-4">
            与 {{ selectedHistory.assistant_name }} 的对话
          </h4>
          
          <!-- 消息列表 -->
          <div class="flex-1 overflow-y-auto space-y-4 max-h-96" style="scroll-behavior: smooth;">
            <div 
              v-for="message in historyMessages" 
              :key="message.id"
              :class="['flex transition-all duration-300 ease-in-out', message.is_user ? 'justify-end' : 'justify-start']"
            >
              <div 
                :class="['max-w-xs lg:max-w-md px-4 py-2 rounded-lg transform transition-all duration-200 ease-in-out hover:scale-[1.02]', 
                         message.is_user ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-900']"
              >
                <div class="flex items-center mb-1">
                  <span v-if="message.message_type === 'voice'" class="text-xs mr-1">
                    {{ message.is_user ? '🎤' : '🔊' }}
                  </span>
                  <span class="font-serif">{{ message.content }}</span>
                </div>
                <div class="text-xs mt-1 opacity-70">{{ formatTime(message.created_at) }}</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="mt-4 flex space-x-3">
            <button 
              @click="restoreConversation"
              class="bg-gradient-to-r from-green-400/80 via-emerald-400/80 via-teal-400/80 via-cyan-400/80 to-blue-400/80 hover:from-green-500/90 hover:via-emerald-500/90 hover:via-teal-500/90 hover:via-cyan-500/90 hover:to-blue-500/90 text-white px-4 py-2 rounded-lg font-serif transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-lg active:scale-95"
            >
              恢复此对话
            </button>
            <button 
              @click="deleteHistory"
              class="bg-gradient-to-r from-red-400/80 via-pink-400/80 via-rose-400/80 via-orange-400/80 to-yellow-400/80 hover:from-red-500/90 hover:via-pink-500/90 hover:via-rose-500/90 hover:via-orange-500/90 hover:to-yellow-500/90 text-white px-4 py-2 rounded-lg font-serif transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-lg active:scale-95"
            >
              删除记录
            </button>
          </div>
        </div>
        
        <div v-else class="flex-1 flex items-center justify-center text-gray-500 font-serif">
          请选择一个对话记录查看详情
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HistoryModal',
  props: {
    show: Boolean,
    assistants: Array
  },
  emits: ['close', 'restore-conversation'],
  data() {
    return {
      selectedAssistantId: '',
      historyList: [],
      selectedHistory: null,
      historyMessages: [],
      isLoading: false
    }
  },
  methods: {
    async fetchHistoryList() {
      if (this.isLoading) return
      this.isLoading = true
      
      try {
        const response = await fetch('http://localhost:8080/api/history/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            assistant_id: this.selectedAssistantId || null
          })
        })
        const result = await response.json()
        if (result.code === 200) {
          this.historyList = result.data || []
        }
      } catch (error) {
        console.error('获取历史对话列表失败:', error)
      } finally {
        this.isLoading = false
      }
    },

    async selectHistory(history) {
      if (this.selectedHistory?.id === history.id) return
      
      this.selectedHistory = history
      this.historyMessages = [] // 立即清空，提供视觉反馈
      
      try {
        const response = await fetch('http://localhost:8080/api/history/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            conversation_id: history.id
          })
        })
        const result = await response.json()
        if (result.code === 200) {
          this.historyMessages = result.data || []
          // 使用 nextTick 确保 DOM 更新后再滚动
          this.$nextTick(() => {
            const messageContainer = document.querySelector('.max-h-96.overflow-y-auto')
            if (messageContainer) {
              messageContainer.scrollTop = messageContainer.scrollHeight
            }
          })
        }
      } catch (error) {
        console.error('获取历史消息失败:', error)
      }
    },

    async deleteHistory() {
      if (!this.selectedHistory) return
      
      if (confirm('确定要删除这个对话记录吗？')) {
        try {
          const response = await fetch('http://localhost:8080/api/history/delete', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              conversation_id: this.selectedHistory.id
            })
          })
          const result = await response.json()
          if (result.code === 200) {
            await this.fetchHistoryList()
            this.selectedHistory = null
            this.historyMessages = []
          }
        } catch (error) {
          console.error('删除历史记录失败:', error)
        }
      }
    },

    restoreConversation() {
      if (this.selectedHistory && this.historyMessages.length > 0) {
        const assistantExists = this.assistants.some(a => a.id == this.selectedHistory.assistant_id)
        
        if (!assistantExists) {
          const confirmRestore = confirm(`助手 "${this.selectedHistory.assistant_name}" 已被删除，是否仍要恢复此对话？`)
          if (!confirmRestore) {
            return
          }
        }
        
        this.$emit('restore-conversation', {
          assistant: {
            id: this.selectedHistory.assistant_id,
            name: this.selectedHistory.assistant_name
          },
          messages: this.historyMessages
        })
        this.$emit('close')
      }
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('zh-CN')
    },

    formatTime(dateString) {
      return new Date(dateString).toLocaleTimeString('zh-CN')
    }
  },

  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.fetchHistoryList()
          })
        } else {
          // 关闭时重置状态
          this.selectedHistory = null
          this.historyMessages = []
          this.selectedAssistantId = ''
        }
      },
      immediate: false
    }
  }
}
</script>

<style scoped>
/* 优化滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 优化按钮点击效果 */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 优化选择框样式 */
select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
