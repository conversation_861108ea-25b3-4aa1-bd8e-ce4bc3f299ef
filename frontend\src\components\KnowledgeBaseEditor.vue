<template>
  <div class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-70" v-if="show">
    <div class="bg-white rounded-2xl shadow-2xl w-[95vw] h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center">
          <button 
            @click="$emit('close')"
            class="mr-3 w-8 h-8 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center justify-center text-gray-600 transition-colors"
          >
            ←
          </button>
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
            📚
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900">{{ knowledgeBase.name }}</h3>
            <p class="text-sm text-gray-600">知识库内容管理</p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <button 
            @click="saveChanges"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            保存更改
          </button>
          <button 
            @click="$emit('close')"
            class="w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
          >
            ✕
          </button>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧文件和分段列表 -->
        <div class="w-1/3 border-r border-gray-200 flex flex-col">
          <!-- 搜索和筛选 -->
          <div class="p-4 border-b border-gray-200">
            <div class="relative mb-3">
              <input 
                v-model="searchQuery"
                placeholder="搜索内容..."
                class="w-full pl-8 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <span class="absolute left-2 top-2.5 text-gray-400">🔍</span>
            </div>
            <div class="flex space-x-2">
              <select v-model="selectedFileFilter" class="flex-1 px-3 py-2 border border-gray-200 rounded-lg text-sm">
                <option value="">全部文件</option>
                <option v-for="file in files" :key="file.id" :value="file.id">{{ file.name }}</option>
              </select>
              <button 
                @click="showAddSegment = true"
                class="px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
              >
                + 添加
              </button>
            </div>
          </div>

          <!-- 分段列表 -->
          <div class="flex-1 overflow-y-auto">
            <div class="p-2 space-y-2">
              <div 
                v-for="segment in filteredSegments" 
                :key="segment.id"
                @click="selectSegment(segment)"
                :class="[
                  'p-3 border rounded-lg cursor-pointer transition-all duration-200',
                  selectedSegment?.id === segment.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                ]"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700">分段 {{ segment.id }}</span>
                  <div class="flex space-x-1">
                    <button 
                      @click.stop="editSegment(segment)"
                      class="text-xs text-blue-600 hover:text-blue-800"
                    >
                      编辑
                    </button>
                    <button 
                      @click.stop="deleteSegment(segment.id)"
                      class="text-xs text-red-600 hover:text-red-800"
                    >
                      删除
                    </button>
                  </div>
                </div>
                <p class="text-xs text-gray-600 mb-1">{{ segment.fileName }}</p>
                <p class="text-sm text-gray-900 line-clamp-3">
                  {{ segment.content.substring(0, 100) }}{{ segment.content.length > 100 ? '...' : '' }}
                </p>
                <div class="flex items-center justify-between mt-2">
                  <span class="text-xs text-gray-500">{{ segment.content.length }} 字符</span>
                  <div class="flex space-x-1">
                    <span v-for="keyword in segment.keywords.slice(0, 2)" :key="keyword"
                          class="px-1 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
                      {{ keyword }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容编辑区域 -->
        <div class="flex-1 flex flex-col">
          <div v-if="selectedSegment" class="flex-1 flex flex-col">
            <!-- 分段信息头部 -->
            <div class="p-4 border-b border-gray-200 bg-gray-50">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900">分段 {{ selectedSegment.id }}</h4>
                  <p class="text-sm text-gray-600">来源文件: {{ selectedSegment.fileName }}</p>
                </div>
                <div class="flex space-x-2">
                  <button 
                    @click="toggleEditMode"
                    :class="[
                      'px-3 py-1 rounded text-sm transition-colors',
                      isEditing ? 'bg-green-500 text-white hover:bg-green-600' : 'bg-blue-500 text-white hover:bg-blue-600'
                    ]"
                  >
                    {{ isEditing ? '保存' : '编辑' }}
                  </button>
                  <button 
                    v-if="isEditing"
                    @click="cancelEdit"
                    class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>

            <!-- 内容编辑区域 -->
            <div class="flex-1 p-4 overflow-y-auto">
              <div class="space-y-6">
                <!-- 内容编辑 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">内容</label>
                  <textarea 
                    v-if="isEditing"
                    v-model="editingContent"
                    class="w-full h-64 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    placeholder="输入分段内容..."
                  ></textarea>
                  <div v-else class="p-3 border border-gray-200 rounded-lg bg-gray-50 min-h-64">
                    <p class="text-gray-900 whitespace-pre-wrap">{{ selectedSegment.content }}</p>
                  </div>
                </div>

                <!-- 关键词编辑 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">关键词</label>
                  <div v-if="isEditing" class="space-y-2">
                    <div class="flex flex-wrap gap-2 mb-2">
                      <span v-for="(keyword, index) in editingKeywords" :key="index"
                            class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                        {{ keyword }}
                        <button @click="removeKeyword(index)" class="ml-1 text-blue-500 hover:text-blue-700">
                          ×
                        </button>
                      </span>
                    </div>
                    <div class="flex">
                      <input 
                        v-model="newKeyword"
                        @keyup.enter="addKeyword"
                        placeholder="输入关键词后按回车添加"
                        class="flex-1 px-3 py-2 border border-gray-200 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                      <button 
                        @click="addKeyword"
                        class="px-4 py-2 bg-blue-500 text-white rounded-r-lg hover:bg-blue-600 transition-colors"
                      >
                        添加
                      </button>
                    </div>
                  </div>
                  <div v-else class="flex flex-wrap gap-2">
                    <span v-for="keyword in selectedSegment.keywords" :key="keyword"
                          class="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded">
                      {{ keyword }}
                    </span>
                  </div>
                </div>

                <!-- 摘要编辑 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">摘要</label>
                  <textarea 
                    v-if="isEditing"
                    v-model="editingSummary"
                    class="w-full h-24 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    placeholder="输入分段摘要..."
                  ></textarea>
                  <div v-else class="p-3 border border-gray-200 rounded-lg bg-gray-50">
                    <p class="text-gray-900">{{ selectedSegment.summary || '暂无摘要' }}</p>
                  </div>
                </div>

                <!-- 元数据 -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">字符数</label>
                    <div class="p-3 border border-gray-200 rounded-lg bg-gray-50">
                      <p class="text-gray-900">{{ selectedSegment.content.length }}</p>
                    </div>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">最后更新</label>
                    <div class="p-3 border border-gray-200 rounded-lg bg-gray-50">
                      <p class="text-gray-900">{{ formatDate(selectedSegment.updatedAt) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 未选择分段时的提示 -->
          <div v-else class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <span class="text-4xl text-gray-400">📄</span>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">选择一个分段进行编辑</h4>
              <p class="text-gray-600">从左侧列表中选择要编辑的内容分段</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加分段模态框 -->
      <div v-if="showAddSegment" class="fixed inset-0 bg-black/50 flex items-center justify-center z-80">
        <div class="bg-white rounded-xl shadow-2xl w-[600px] max-h-[80vh] overflow-hidden">
          <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 class="text-lg font-bold text-gray-900">添加新分段</h3>
            <button @click="showAddSegment = false" class="text-gray-500 hover:text-gray-700">✕</button>
          </div>
          
          <div class="p-6 space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">所属文件</label>
              <select v-model="newSegment.fileId" class="w-full px-3 py-2 border border-gray-200 rounded-lg">
                <option value="">选择文件</option>
                <option v-for="file in files" :key="file.id" :value="file.id">{{ file.name }}</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">内容</label>
              <textarea 
                v-model="newSegment.content"
                class="w-full h-32 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="输入分段内容..."
              ></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">关键词 (用逗号分隔)</label>
              <input 
                v-model="newSegment.keywordsText"
                class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="关键词1, 关键词2, 关键词3"
              >
            </div>
          </div>
          
          <div class="flex justify-end space-x-4 p-6 border-t border-gray-200">
            <button @click="showAddSegment = false" class="px-4 py-2 text-gray-600 hover:text-gray-800">
              取消
            </button>
            <button @click="addNewSegment" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
              添加
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeBaseEditor',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    knowledgeBase: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'save'],

  data() {
    return {
      searchQuery: '',
      selectedFileFilter: '',
      selectedSegment: null,
      isEditing: false,
      editingContent: '',
      editingKeywords: [],
      editingSummary: '',
      newKeyword: '',
      showAddSegment: false,
      newSegment: {
        fileId: '',
        content: '',
        keywordsText: ''
      },
      
      // 模拟数据
      files: [
        { id: 1, name: 'go.docx' },
        { id: 2, name: 'product_manual.pdf' },
        { id: 3, name: 'api_docs.md' }
      ],
      
      segments: [
        {
          id: 1,
          fileId: 1,
          fileName: 'go.docx',
          content: 'Go 知识库\n\nGo 简介入门\n\n1.1 Go 介绍\nGo 是 Google 开发的一种静态强类型、编译型语言。Go 语言语法与 C 相近，但功能上有：内存安全，GC（垃圾回收），结构形态及 CSP-style 并发计算。',
          keywords: ['Go语言', 'Google', '编程语言', '静态类型'],
          summary: 'Go语言的基本介绍，包括其特性和优势',
          updatedAt: new Date('2024-01-15')
        },
        {
          id: 2,
          fileId: 1,
          fileName: 'go.docx',
          content: '1.2 开发环境搭建\n下载安装\n从 Go 官方网站下载对应操作系统的 Go 安装包。Windows、macOS、Linux 都有对应的安装包。安装完成后，可以通过命令行验证安装是否成功。',
          keywords: ['环境搭建', '安装', '下载'],
          summary: 'Go语言开发环境的安装和配置步骤',
          updatedAt: new Date('2024-01-15')
        },
        {
          id: 3,
          fileId: 2,
          fileName: 'product_manual.pdf',
          content: '产品功能介绍\n\n本产品是一个智能知识库管理系统，支持多种文档格式的导入、解析和检索。主要功能包括：\n1. 文档上传和解析\n2. 智能分段\n3. 关键词提取\n4. 语义检索',
          keywords: ['产品介绍', '知识库', '文档管理'],
          summary: '产品的主要功能和特性介绍',
          updatedAt: new Date('2024-01-10')
        }
      ]
    }
  },

  computed: {
    filteredSegments() {
      let filtered = this.segments
      
      // 按文件筛选
      if (this.selectedFileFilter) {
        filtered = filtered.filter(s => s.fileId == this.selectedFileFilter)
      }
      
      // 按搜索关键词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(s => 
          s.content.toLowerCase().includes(query) ||
          s.keywords.some(k => k.toLowerCase().includes(query))
        )
      }
      
      return filtered
    }
  },

  methods: {
    selectSegment(segment) {
      this.selectedSegment = segment
      this.isEditing = false
    },

    editSegment(segment) {
      this.selectSegment(segment)
      this.startEdit()
    },

    startEdit() {
      this.isEditing = true
      this.editingContent = this.selectedSegment.content
      this.editingKeywords = [...this.selectedSegment.keywords]
      this.editingSummary = this.selectedSegment.summary || ''
    },

    toggleEditMode() {
      if (this.isEditing) {
        this.saveSegmentChanges()
      } else {
        this.startEdit()
      }
    },

    cancelEdit() {
      this.isEditing = false
      this.editingContent = ''
      this.editingKeywords = []
      this.editingSummary = ''
    },

    saveSegmentChanges() {
      if (this.selectedSegment) {
        this.selectedSegment.content = this.editingContent
        this.selectedSegment.keywords = [...this.editingKeywords]
        this.selectedSegment.summary = this.editingSummary
        this.selectedSegment.updatedAt = new Date()
        this.isEditing = false
      }
    },

    addKeyword() {
      if (this.newKeyword.trim() && !this.editingKeywords.includes(this.newKeyword.trim())) {
        this.editingKeywords.push(this.newKeyword.trim())
        this.newKeyword = ''
      }
    },

    removeKeyword(index) {
      this.editingKeywords.splice(index, 1)
    },

    deleteSegment(segmentId) {
      if (confirm('确定要删除这个分段吗？')) {
        this.segments = this.segments.filter(s => s.id !== segmentId)
        if (this.selectedSegment?.id === segmentId) {
          this.selectedSegment = null
        }
      }
    },

    addNewSegment() {
      if (!this.newSegment.fileId || !this.newSegment.content.trim()) {
        alert('请填写完整信息')
        return
      }

      const file = this.files.find(f => f.id == this.newSegment.fileId)
      const keywords = this.newSegment.keywordsText
        .split(',')
        .map(k => k.trim())
        .filter(k => k)

      const segment = {
        id: Math.max(...this.segments.map(s => s.id)) + 1,
        fileId: this.newSegment.fileId,
        fileName: file.name,
        content: this.newSegment.content,
        keywords: keywords,
        summary: '',
        updatedAt: new Date()
      }

      this.segments.push(segment)
      this.showAddSegment = false
      this.newSegment = { fileId: '', content: '', keywordsText: '' }
    },

    saveChanges() {
      // 保存所有更改
      const result = {
        knowledgeBase: this.knowledgeBase,
        segments: this.segments
      }
      this.$emit('save', result)
      alert('保存成功！')
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
