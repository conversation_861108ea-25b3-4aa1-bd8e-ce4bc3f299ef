<template>
  <div class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-70" v-if="show">
    <div class="bg-white rounded-2xl shadow-2xl w-[95vw] h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center">
          <button 
            @click="handleBack"
            class="mr-3 w-8 h-8 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center justify-center text-gray-600 transition-colors"
          >
            ←
          </button>
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
            📚
          </div>
          <h3 class="text-xl font-bold text-gray-900">{{ getStepTitle() }}</h3>
        </div>
        <button 
          @click="$emit('close')"
          class="w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
        >
          ✕
        </button>
      </div>

      <!-- 步骤指示器 -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-center space-x-8">
          <div v-for="(step, index) in steps" :key="index" class="flex items-center">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold',
              currentStep >= index + 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
            ]">
              {{ index + 1 }}
            </div>
            <span :class="[
              'ml-2 text-sm font-medium',
              currentStep >= index + 1 ? 'text-blue-600' : 'text-gray-500'
            ]">
              {{ step }}
            </span>
            <div v-if="index < steps.length - 1" :class="[
              'w-16 h-0.5 ml-4',
              currentStep > index + 1 ? 'bg-blue-500' : 'bg-gray-200'
            ]"></div>
          </div>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="flex-1 overflow-y-auto">
        <!-- 步骤1: 上传文件 -->
        <div v-if="currentStep === 1" class="p-6">
          <div class="text-center py-16">
            <div class="w-24 h-24 mx-auto mb-6 border-2 border-dashed border-gray-300 rounded-xl flex items-center justify-center hover:border-blue-400 transition-colors cursor-pointer"
                 @click="triggerFileUpload"
                 @dragover.prevent
                 @drop.prevent="handleFileDrop">
              <div class="text-center">
                <span class="text-4xl text-gray-400 block mb-2">📁</span>
                <p class="text-sm text-gray-600">点击上传或拖拽文件到此处</p>
              </div>
            </div>
            <input ref="fileInput" type="file" multiple accept=".docx,.txt,.md" class="hidden" @change="handleFileSelect">

            <p class="text-gray-600 mb-4">支持 Word文档(.docx)、Markdown(.md)、文本文件(.txt)，单个文件不超过 50MB</p>
            
            <!-- 已上传文件列表 -->
            <div v-if="uploadedFiles.length > 0" class="mt-8">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">已上传文件</h4>
              <div class="space-y-3">
                <div v-for="file in uploadedFiles" :key="file.id"
                     class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div :class="[
                      'w-10 h-10 rounded-lg flex items-center justify-center text-white',
                      file.status === 'error' ? 'bg-red-500' :
                      file.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                    ]">
                      <span v-if="file.status === 'error'">❌</span>
                      <span v-else-if="file.status === 'completed'">✅</span>
                      <span v-else-if="file.status === 'parsing'">⚙️</span>
                      <span v-else>📄</span>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-900">{{ file.name }}</h5>
                      <p class="text-sm text-gray-600">{{ formatFileSize(file.size) }}</p>
                      <div class="flex items-center space-x-2 text-xs">
                        <span v-if="file.segments.length > 0" class="text-blue-600">
                          已分段: {{ file.segments.length }} 个
                        </span>
                        <span v-if="file.aiEnhanced" class="px-1 py-0.5 bg-green-100 text-green-700 rounded">
                          AI增强
                        </span>
                        <span v-if="file.analysis" class="px-1 py-0.5 bg-purple-100 text-purple-700 rounded">
                          {{ file.analysis.documentType }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="text-right">
                      <div v-if="file.status === 'error'" class="text-sm text-red-600">
                        解析失败
                      </div>
                      <div v-else-if="file.status === 'parsing'" class="text-sm text-blue-600">
                        正在解析...
                      </div>
                      <div v-else-if="file.status === 'completed'" class="text-sm text-green-600">
                        {{ file.progress }}% 完成
                      </div>
                      <div v-else class="text-sm text-gray-600">
                        {{ file.progress }}% 上传中
                      </div>

                      <!-- 进度条 -->
                      <div v-if="file.status !== 'completed' && file.status !== 'error'"
                           class="w-20 h-1 bg-gray-200 rounded-full mt-1">
                        <div class="h-1 bg-blue-500 rounded-full transition-all duration-300"
                             :style="{ width: file.progress + '%' }"></div>
                      </div>
                    </div>
                    <button @click="removeFile(file.id)" class="text-red-500 hover:text-red-700">
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2: 创建设置 -->
        <div v-if="currentStep === 2" class="p-6">
          <div class="max-w-4xl mx-auto space-y-6">
            <!-- 文档解析设置 -->
            <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">文档解析设置</h4>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">解析模式</label>
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input type="checkbox" v-model="settings.parseText" class="mr-2">
                      <span>纯文本 (OCR)</span>
                    </label>
                    <label class="flex items-center">
                      <input type="checkbox" v-model="settings.parseTable" class="mr-2">
                      <span>表格元素</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">内容过滤</label>
                  <textarea v-model="settings.contentFilter" 
                           placeholder="输入需要过滤的内容关键词，多个关键词用换行分隔"
                           class="w-full h-24 p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
              </div>
            </div>

            <!-- AI处理选项 -->
            <div v-if="aiEnabled" class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                AI智能处理
              </h4>
              <div class="grid grid-cols-2 gap-4 mb-3">
                <div class="flex items-center">
                  <input v-model="settings.useAI" type="checkbox" id="useAI"
                         class="w-4 h-4 text-blue-600 border-gray-300 rounded">
                  <label for="useAI" class="ml-2 text-sm text-gray-700">启用AI增强分段</label>
                </div>
                <div class="flex items-center">
                  <input v-model="settings.fallbackToLocal" type="checkbox" id="fallback"
                         class="w-4 h-4 text-blue-600 border-gray-300 rounded">
                  <label for="fallback" class="ml-2 text-sm text-gray-700">AI失败时本地备选</label>
                </div>
              </div>
              <p class="text-xs text-gray-600">
                AI增强功能可提供更准确的语义分段、关键词提取和摘要生成
              </p>
            </div>

            <!-- 分段设置 -->
            <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">分段设置</h4>
              
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">分段策略</label>
                  <select v-model="settings.segmentStrategy" 
                          class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="auto">自动分段</option>
                    <option value="manual">手动分段</option>
                    <option value="fixed">固定长度</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">段落长度</label>
                  <input type="number" v-model="settings.segmentLength" 
                         placeholder="建议500-2000字符"
                         class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">段落重叠</label>
                  <input type="number" v-model="settings.segmentOverlap" 
                         placeholder="重叠字符数，建议50-200"
                         class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">分隔符</label>
                  <input type="text" v-model="settings.separator" 
                         placeholder="自定义分段分隔符"
                         class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
              </div>
            </div>

            <!-- 索引设置 -->
            <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">索引设置</h4>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">关键词提取</label>
                  <label class="flex items-center">
                    <input type="checkbox" v-model="settings.extractKeywords" class="mr-2">
                    <span>自动提取关键词用于检索优化</span>
                  </label>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">摘要生成</label>
                  <label class="flex items-center">
                    <input type="checkbox" v-model="settings.generateSummary" class="mr-2">
                    <span>为每个段落生成摘要</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 分段预览 -->
        <div v-if="currentStep === 3" class="p-6">
          <div class="flex h-full">
            <!-- 左侧文件列表 -->
            <div class="w-1/4 border-r border-gray-200 pr-4">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">文档列表</h4>
              <div class="space-y-2">
                <div v-for="file in uploadedFiles" :key="file.id"
                     @click="selectedFileId = file.id"
                     :class="[
                       'p-3 border rounded-lg cursor-pointer transition-colors',
                       selectedFileId === file.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                     ]">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <div :class="[
                        'w-3 h-3 rounded-full',
                        file.status === 'completed' ? 'bg-green-500' :
                        file.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                      ]"></div>
                      <h5 class="font-medium text-gray-900 text-sm">{{ file.name }}</h5>
                    </div>
                    <span v-if="file.status === 'parsing'" class="text-xs text-blue-600">解析中...</span>
                    <span v-else-if="file.status === 'error'" class="text-xs text-red-600">解析失败</span>
                  </div>
                  <div class="flex items-center justify-between mt-1">
                    <p class="text-xs text-gray-600">{{ file.segments?.length || 0 }} 个分段</p>
                    <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                  </div>
                  <div v-if="file.error" class="text-xs text-red-600 mt-1">
                    错误: {{ file.error }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧分段预览 -->
            <div class="flex-1 pl-6">
              <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-semibold text-gray-900">分段预览</h4>
                <div class="text-sm text-gray-600">
                  共 {{ getCurrentFileSegments().length }} 个分段
                </div>
              </div>

              <div class="space-y-4 max-h-96 overflow-y-auto">
                <div v-for="segment in getCurrentFileSegments()" :key="segment.id"
                     class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                  <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center space-x-2">
                      <span class="text-sm font-medium text-gray-700">分段 {{ segment.id }}</span>
                      <span v-if="segment.aiEnhanced" class="px-1 py-0.5 bg-green-100 text-green-700 text-xs rounded">
                        AI增强
                      </span>
                      <span v-if="segment.source" class="px-1 py-0.5 bg-gray-100 text-gray-600 text-xs rounded">
                        {{ segment.source }}
                      </span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="text-xs text-gray-500">{{ segment.content.length }} 字符</span>
                      <span class="text-xs text-blue-500">{{ segment.keywords.length }} 关键词</span>
                    </div>
                  </div>

                  <!-- 分段内容 -->
                  <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded mb-3">
                    {{ segment.content.substring(0, 300) }}{{ segment.content.length > 300 ? '...' : '' }}
                  </div>

                  <!-- 关键词 -->
                  <div v-if="segment.keywords.length > 0" class="mb-2">
                    <span class="text-xs text-gray-600 mr-2">关键词:</span>
                    <span v-for="keyword in segment.keywords" :key="keyword"
                          class="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded mr-1">
                      {{ keyword }}
                    </span>
                  </div>

                  <!-- 摘要 -->
                  <div v-if="segment.summary" class="mb-3">
                    <span class="text-xs text-gray-600">摘要:</span>
                    <p class="text-xs text-gray-700 mt-1">{{ segment.summary }}</p>
                  </div>

                  <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-500">
                      创建时间: {{ formatDate(segment.createdAt) }}
                    </div>
                    <div class="flex space-x-2">
                      <button @click="editSegment(segment)" class="text-xs text-blue-600 hover:text-blue-800">编辑</button>
                      <button @click="deleteSegment(segment.id)" class="text-xs text-red-600 hover:text-red-800">删除</button>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="getCurrentFileSegments().length === 0" class="text-center py-8">
                  <div class="text-gray-400 mb-2">📄</div>
                  <p class="text-sm text-gray-600">该文件暂无分段内容</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤4: 数据处理 -->
        <div v-if="currentStep === 4" class="p-6">
          <div class="text-center py-16">
            <div class="w-24 h-24 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
              <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <h4 class="text-xl font-semibold text-gray-900 mb-2">正在处理知识库数据</h4>
            <p class="text-gray-600 mb-6">请稍候，正在为您的知识库建立索引...</p>

            <!-- 处理统计信息 -->
            <div v-if="getProcessingStats()" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 max-w-2xl mx-auto">
              <div class="bg-blue-50 p-3 rounded-lg text-center">
                <div class="text-xl font-bold text-blue-600">{{ getProcessingStats().totalFiles }}</div>
                <div class="text-xs text-gray-600">总文件数</div>
              </div>
              <div class="bg-green-50 p-3 rounded-lg text-center">
                <div class="text-xl font-bold text-green-600">{{ getProcessingStats().totalSegments }}</div>
                <div class="text-xs text-gray-600">总分段数</div>
              </div>
              <div class="bg-purple-50 p-3 rounded-lg text-center">
                <div class="text-xl font-bold text-purple-600">{{ getProcessingStats().aiEnhancedFiles }}</div>
                <div class="text-xs text-gray-600">AI增强文件</div>
              </div>
              <div class="bg-orange-50 p-3 rounded-lg text-center">
                <div class="text-xl font-bold text-orange-600">{{ getProcessingStats().avgSegmentsPerFile }}</div>
                <div class="text-xs text-gray-600">平均分段数</div>
              </div>
            </div>

            <div class="max-w-md mx-auto">
              <div class="bg-gray-200 rounded-full h-2 mb-4">
                <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                     :style="{ width: processingProgress + '%' }"></div>
              </div>
              <p class="text-sm text-gray-600">{{ processingProgress }}% 完成</p>
            </div>

            <div class="mt-8 text-left max-w-md mx-auto">
              <h5 class="font-semibold text-gray-900 mb-2">处理进度：</h5>
              <div class="space-y-2 text-sm">
                <div class="flex items-center">
                  <span class="text-green-500 mr-2">✓</span>
                  <span>文档解析完成</span>
                </div>
                <div class="flex items-center">
                  <span class="text-green-500 mr-2">✓</span>
                  <span>内容分段完成</span>
                </div>
                <div class="flex items-center">
                  <span :class="processingProgress >= 70 ? 'text-green-500' : 'text-gray-400'" class="mr-2">
                    {{ processingProgress >= 70 ? '✓' : '○' }}
                  </span>
                  <span>向量化处理</span>
                </div>
                <div class="flex items-center">
                  <span :class="processingProgress >= 100 ? 'text-green-500' : 'text-gray-400'" class="mr-2">
                    {{ processingProgress >= 100 ? '✓' : '○' }}
                  </span>
                  <span>索引建立</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <button 
          v-if="currentStep > 1 && currentStep < 4"
          @click="previousStep"
          class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          上一步
        </button>
        <div v-else></div>

        <div class="flex space-x-4">
          <button 
            @click="$emit('close')"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            取消
          </button>
          <button 
            v-if="currentStep < 4"
            @click="nextStep"
            :disabled="!canProceedToNext()"
            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            {{ currentStep === 3 ? '开始处理' : '下一步' }}
          </button>
          <button 
            v-if="currentStep === 4 && processingProgress === 100"
            @click="completeCreation"
            class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            完成创建
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import EnhancedDocumentParser from '../utils/enhancedDocumentParser.js'
import aiConfig from '../config/aiConfig.js'

export default {
  name: 'KnowledgeBaseManager',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    knowledgeBase: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'complete'],

  data() {
    return {
      currentStep: 1,
      steps: ['上传', '创建设置', '分段预览', '数据处理'],
      
      // 上传文件
      uploadedFiles: [],
      
      // 设置
      settings: {
        parseText: true,
        parseTable: false,
        contentFilter: '',
        segmentStrategy: 'auto',
        segmentLength: 1000,
        segmentOverlap: 100,
        separator: '',
        extractKeywords: true,
        generateSummary: false,
        useAI: true,
        fallbackToLocal: true
      },

      // 增强版解析器
      parser: null,

      // AI处理状态
      aiProcessing: false,
      aiEnabled: false,

      // 处理统计
      processingStats: null,

      // 分段预览
      selectedFileId: null,

      // 数据处理
      processingProgress: 0
    }
  },

  mounted() {
    this.initializeParser()
    this.loadAIConfig()
  },

  methods: {
    /**
     * 初始化增强版解析器
     */
    initializeParser() {
      try {
        this.parser = new EnhancedDocumentParser({
          bailian: aiConfig.getBailianConfig(),
          useAI: this.settings.useAI,
          fallbackToLocal: this.settings.fallbackToLocal
        })
        this.aiEnabled = aiConfig.isBailianConfigured()
        console.log('增强版解析器初始化成功')

        // 测试分段功能
        this.testSegmentation()
      } catch (error) {
        console.error('初始化解析器失败:', error)
        this.aiEnabled = false
      }
    },

    async testSegmentation() {
      try {
        console.log('测试分段功能...')
        const testContent = `这是第一段内容。这里有一些测试文字，用来验证分段功能是否正常工作。

这是第二段内容。这里也有一些测试文字，用来验证分段功能的效果。

这是第三段内容。最后一段用来确保分段算法能够正确处理多段文本。`

        const segments = await this.parser.segmentDocument(testContent, {
          strategy: 'auto',
          maxLength: 100,
          overlap: 20,
          useAI: false
        })

        console.log('测试分段结果:', segments)
        console.log('测试分段数量:', segments.length)
      } catch (error) {
        console.error('测试分段失败:', error)
      }
    },

    /**
     * 加载AI配置
     */
    loadAIConfig() {
      const config = aiConfig.getProcessingConfig()
      this.settings.useAI = config.useAI
      this.settings.fallbackToLocal = config.fallbackToLocal

      const segmentConfig = aiConfig.getSegmentationConfig()
      this.settings.segmentStrategy = segmentConfig.defaultStrategy
      this.settings.segmentLength = segmentConfig.maxLength
      this.settings.segmentOverlap = segmentConfig.overlap
    },
    getStepTitle() {
      const titles = {
        1: '新增知识库 - 上传文件',
        2: '新增知识库 - 创建设置', 
        3: '新增知识库 - 分段预览',
        4: '新增知识库 - 数据处理'
      }
      return titles[this.currentStep] || '新增知识库'
    },

    handleBack() {
      if (this.currentStep > 1) {
        this.previousStep()
      } else {
        this.$emit('close')
      }
    },

    triggerFileUpload() {
      this.$refs.fileInput.click()
    },

    handleFileSelect(event) {
      const files = Array.from(event.target.files)
      this.addFiles(files)
    },

    handleFileDrop(event) {
      const files = Array.from(event.dataTransfer.files)
      this.addFiles(files)
    },

    async addFiles(files) {
      for (const file of files) {
        const fileObj = {
          id: Date.now() + Math.random(),
          name: file.name,
          size: file.size,
          file: file,
          progress: 0,
          status: 'uploading',
          content: '',
          segments: [],
          analysis: null,
          error: null,
          aiEnhanced: false
        }

        this.uploadedFiles.push(fileObj)

        try {
          // 模拟上传进度
          await this.simulateUploadProgress(fileObj)

          // 使用增强版解析器解析文档
          fileObj.status = 'parsing'
          const parseResult = await this.parser.parseDocument(file, {
            useAI: this.settings.useAI && this.aiEnabled
          })

          // 更新文件对象
          Object.assign(fileObj, parseResult)

          // 生成初始分段
          console.log('开始分段处理，文件:', fileObj.name, '内容长度:', fileObj.content.length)
          fileObj.status = 'segmenting'

          try {
            const segmentSettings = {
              strategy: this.settings.segmentStrategy,
              maxLength: parseInt(this.settings.segmentLength),
              overlap: parseInt(this.settings.segmentOverlap),
              useAI: this.settings.useAI && this.aiEnabled
            }
            console.log('分段设置:', segmentSettings)

            fileObj.segments = await this.parser.segmentDocument(
              fileObj.content,
              segmentSettings,
              fileObj.analysis
            )

            console.log('分段完成，分段数量:', fileObj.segments.length)
            console.log('分段结果:', fileObj.segments)
            console.log('文件对象完整信息:', JSON.stringify(fileObj, null, 2))

            fileObj.status = 'completed'
            fileObj.progress = 100
            fileObj.aiEnhanced = this.settings.useAI && this.aiEnabled

            // 强制Vue更新以确保响应式数据变化被检测到
            this.$forceUpdate()
            console.log('强制更新Vue组件')

          } catch (segmentError) {
            console.error('分段处理失败:', segmentError)
            // 即使分段失败，也标记为完成，但没有分段
            fileObj.segments = []
            fileObj.status = 'completed'
            fileObj.progress = 100
            fileObj.error = `分段失败: ${segmentError.message}`
          }

        } catch (error) {
          console.error('文件处理失败:', error)
          fileObj.status = 'error'
          fileObj.error = error.message
        }
      }
    },

    async simulateUploadProgress(fileObj) {
      return new Promise((resolve) => {
        const interval = setInterval(() => {
          fileObj.progress += Math.random() * 20
          if (fileObj.progress >= 90) {
            fileObj.progress = 90
            clearInterval(interval)
            resolve()
          }
        }, 200)
      })
    },

    removeFile(fileId) {
      this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId)
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    getCurrentFileSegments() {
      console.log('getCurrentFileSegments 被调用')
      console.log('selectedFileId:', this.selectedFileId)
      console.log('uploadedFiles:', this.uploadedFiles)

      if (!this.selectedFileId) {
        const firstFile = this.uploadedFiles[0]
        const segments = firstFile?.segments || []
        console.log('获取第一个文件的分段:')
        console.log('- 文件名:', firstFile?.name)
        console.log('- 文件ID:', firstFile?.id)
        console.log('- 文件状态:', firstFile?.status)
        console.log('- 分段数量:', segments.length)
        console.log('- 分段内容:', segments)
        return segments
      }

      console.log('查找文件，selectedFileId:', this.selectedFileId)
      console.log('所有文件ID:', this.uploadedFiles.map(f => f.id))

      const file = this.uploadedFiles.find(f => f.id === this.selectedFileId)
      console.log('找到的文件:', file)

      if (!file) {
        console.log('未找到匹配的文件，使用第一个文件')
        const firstFile = this.uploadedFiles[0]
        const segments = firstFile?.segments || []
        console.log('- 第一个文件名:', firstFile?.name)
        console.log('- 第一个文件ID:', firstFile?.id)
        console.log('- 分段数量:', segments.length)
        return segments
      }

      const segments = file?.segments || []
      console.log('获取选中文件的分段:')
      console.log('- 文件名:', file?.name)
      console.log('- 文件ID:', file?.id)
      console.log('- 文件状态:', file?.status)
      console.log('- 分段数量:', segments.length)
      console.log('- 分段内容:', segments)
      return segments
    },

    canProceedToNext() {
      switch (this.currentStep) {
        case 1:
          return this.uploadedFiles.length > 0
        case 2:
          return true // 设置可以使用默认值
        case 3:
          return true
        default:
          return false
      }
    },

    async nextStep() {
      if (this.canProceedToNext() && this.currentStep < 4) {
        this.currentStep++

        if (this.currentStep === 3) {
          // 进入分段预览时，根据当前设置重新生成分段
          await this.regenerateSegments()
          if (this.uploadedFiles.length > 0) {
            this.selectedFileId = this.uploadedFiles[0].id
          }
        }

        if (this.currentStep === 4) {
          this.startProcessing()
        }
      }
    },

    async regenerateSegments() {
      this.aiProcessing = true

      try {
        for (const file of this.uploadedFiles) {
          if (file.content && file.status === 'completed') {
            try {
              console.log('重新分段文件:', file.name, '内容长度:', file.content.length)
              file.status = 'segmenting'

              const segmentSettings = {
                strategy: this.settings.segmentStrategy,
                maxLength: parseInt(this.settings.segmentLength) || 1000,
                overlap: parseInt(this.settings.segmentOverlap) || 100,
                separator: this.settings.separator || '\n\n',
                useAI: this.settings.useAI && this.aiEnabled
              }
              console.log('重新分段设置:', segmentSettings)

              // 使用增强版解析器重新分段
              file.segments = await this.parser.segmentDocument(
                file.content,
                segmentSettings,
                file.analysis
              )

              console.log('重新分段完成，分段数量:', file.segments.length)
              file.status = 'completed'
              file.aiEnhanced = this.settings.useAI && this.aiEnabled

            } catch (error) {
              console.error('重新分段失败:', error)
              file.status = 'error'
              file.error = error.message
              file.segments = [] // 确保有一个空数组
            }
          }
        }
      } finally {
        this.aiProcessing = false
      }
    },


    previousStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },

    startProcessing() {
      // 模拟数据处理进度
      this.processingProgress = 0
      const interval = setInterval(() => {
        this.processingProgress += Math.random() * 15
        if (this.processingProgress >= 100) {
          this.processingProgress = 100
          clearInterval(interval)
        }
      }, 500)
    },

    async editSegment(segment) {
      const newContent = prompt('编辑分段内容:', segment.content)
      if (newContent !== null && newContent.trim()) {
        const oldContent = segment.content
        segment.content = newContent.trim()
        segment.length = segment.content.length

        try {
          // 如果启用AI，使用AI重新生成关键词和摘要
          if (this.settings.useAI && this.aiEnabled) {
            try {
              const [keywords, summary] = await Promise.all([
                this.parser.bailianService.extractKeywords(segment.content).catch((error) => {
                  console.log('AI关键词提取失败，使用本地方法:', error.message)
                  return this.parser.localParser.extractKeywords(segment.content)
                }),
                this.parser.bailianService.generateSummary(segment.content).catch((error) => {
                  console.log('AI摘要生成失败，使用本地方法:', error.message)
                  return this.parser.localParser.generateSummary(segment.content)
                })
              ])
              segment.keywords = keywords
              segment.summary = summary
              segment.aiEnhanced = true
            } catch (aiError) {
              console.warn('AI处理失败，回退到本地方法:', aiError.message)
              // 完全回退到本地方法
              segment.keywords = this.parser.localParser.extractKeywords(segment.content)
              segment.summary = this.parser.localParser.generateSummary(segment.content)
              segment.aiEnhanced = false
            }
          } else {
            // 使用本地方法
            segment.keywords = this.parser.localParser.extractKeywords(segment.content)
            segment.summary = this.parser.localParser.generateSummary(segment.content)
            segment.aiEnhanced = false
          }
        } catch (error) {
          console.error('更新分段信息失败:', error)
          // 回退到原内容
          segment.content = oldContent
          segment.length = oldContent.length
        }
      }
    },

    deleteSegment(segmentId) {
      if (confirm('确定要删除这个分段吗？')) {
        const file = this.uploadedFiles.find(f => f.id === this.selectedFileId)
        if (file) {
          file.segments = file.segments.filter(s => s.id !== segmentId)
        }
      }
    },

    completeCreation() {
      const result = {
        ...this.knowledgeBase,
        files: this.uploadedFiles,
        settings: this.settings,
        status: 'completed',
        createdAt: new Date()
      }

      this.$emit('complete', result)
      this.$emit('close')
    },

    formatDate(date) {
      return new Date(date).toLocaleString('zh-CN')
    },

    /**
     * 获取分段策略描述
     */
    getStrategyDescription(strategy) {
      const descriptions = {
        'auto': '根据文档结构自动选择最佳分段方式',
        'semantic': '基于语义边界进行智能分段',
        'fixed': '按固定字符长度分段',
        'manual': '使用自定义分隔符手动分段'
      }
      return descriptions[strategy] || '未知策略'
    },

    /**
     * 获取处理统计信息
     */
    getProcessingStats() {
      if (!this.parser) return null

      const completedFiles = this.uploadedFiles.filter(f => f.status === 'completed')
      const totalSegments = completedFiles.reduce((sum, f) => sum + (f.segments?.length || 0), 0)
      const aiEnhancedFiles = completedFiles.filter(f => f.aiEnhanced).length

      return {
        totalFiles: this.uploadedFiles.length,
        completedFiles: completedFiles.length,
        totalSegments,
        aiEnhancedFiles,
        avgSegmentsPerFile: completedFiles.length > 0 ? Math.round(totalSegments / completedFiles.length) : 0
      }
    }
  },

  watch: {
    show(newVal) {
      if (newVal) {
        // 重置状态
        this.currentStep = 1
        this.uploadedFiles = []
        this.selectedFileId = null
        this.processingProgress = 0
      }
    }
  }
}
</script>
