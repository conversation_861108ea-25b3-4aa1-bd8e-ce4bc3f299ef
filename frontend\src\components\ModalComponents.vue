<template>
  <!-- 新增/编辑助手弹窗 -->
  <div 
    v-if="showAddModal" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    @click.self="$emit('close-add')"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-96 p-8 transform transition-all duration-300 scale-100">
      <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center font-serif">
        <span class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif">
          {{ isEditing ? '✎' : '+' }}
        </span>
        {{ isEditing ? '编辑助手' : '新增助手' }}
      </h3>
      
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-semibold text-gray-700 mb-3 font-serif">助手名称</label>
          <input 
            :value="currentAssistant.name"
            @input="$emit('update-name', $event.target.value)"
            type="text" 
            placeholder="请输入助手名称"
            class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white font-serif"
          >
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-gray-700 mb-3 font-serif">助手描述</label>
          <textarea 
            :value="currentAssistant.description"
            @input="$emit('update-description', $event.target.value)"
            placeholder="请输入助手描述"
            rows="4"
            class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white font-serif"
          ></textarea>
        </div>
      </div>
      
      <div class="flex justify-end space-x-4 mt-8">
        <button 
          @click="$emit('save')"
          class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          {{ isEditing ? '保存' : '确定' }}
        </button>
        <button 
          @click="$emit('close-add')"
          class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          取消
        </button>
      </div>
    </div>
  </div>

  <!-- 人设配置弹窗 -->
  <div 
    v-if="showSettingsModal" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    @click.self="$emit('close-settings')"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-[500px] p-8 transform transition-all duration-300 scale-100">
      <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center font-serif">
        <span class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif">
          ⚙️
        </span>
        {{ currentSettingsAssistant?.name }} - 人设配置
      </h3>
      
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-semibold text-gray-700 mb-3 font-serif">人设配置</label>
          <textarea 
            :value="currentPersonality"
            @input="$emit('update-personality', $event.target.value)"
            placeholder="请输入助手的人设配置，例如：你是一个专业的客服助手，性格温和，善于解决问题..."
            rows="8"
            class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white font-serif"
          ></textarea>
        </div>
      </div>
      
      <div class="flex justify-end space-x-4 mt-8">
        <button 
          @click="$emit('save-personality')"
          class="px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          保存
        </button>
        <button 
          @click="$emit('close-settings')"
          class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          取消
        </button>
      </div>
    </div>
  </div>

  <!-- 确认删除弹窗 -->
  <div 
    v-if="showDeleteModal" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    @click.self="$emit('close-delete')"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-96 p-8 transform transition-all duration-300 scale-100">
      <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center font-serif">
        <span class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center text-white text-sm mr-3 font-serif">
          ⚠️
        </span>
        确认删除
      </h3>
      
      <div class="mb-6">
        <p class="text-gray-700 text-sm leading-relaxed font-serif">
          确定要删除助手 <span class="font-semibold text-red-600 font-serif">"{{ deleteAssistantName }}"</span> 吗？
        </p>
        <p class="text-gray-500 text-xs mt-2 font-serif">
          删除后将无法恢复，包括所有对话记录和人设配置。
        </p>
      </div>
      
      <div class="flex justify-end space-x-4">
        <button 
          @click="$emit('confirm-delete')"
          class="px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          确认删除
        </button>
        <button 
          @click="$emit('close-delete')"
          class="px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-serif"
        >
          取消
        </button>
      </div>
    </div>
  </div>

  <!-- 拨号弹窗 -->
  <div 
    v-if="showDialModal" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    @click.self="$emit('close-dial')"
  >
    <div class="bg-white rounded-3xl shadow-2xl w-80 p-8 transform transition-all duration-300 scale-100">
      <!-- 关闭按钮 -->
      <div class="flex justify-between items-center mb-8">
        <div class="flex items-center">
          <span class="text-gray-300 mr-3 text-lg font-serif">|</span>
          <span class="text-gray-700 text-lg font-medium font-serif">{{ dialNumber || '请输入号码' }}</span>
        </div>
        <button 
          @click="$emit('close-dial')"
          class="text-gray-400 hover:text-gray-600 text-2xl w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-all duration-200 font-serif"
        >
          ×
        </button>
      </div>

      <!-- 数字键盘 -->
      <div class="grid grid-cols-3 gap-4 mb-8">
        <button 
          v-for="number in [1,2,3,4,5,6,7,8,9,'*',0,'#']" 
          :key="number"
          @click="$emit('add-number', number)"
          class="w-16 h-16 bg-gradient-to-b from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100 rounded-2xl flex items-center justify-center text-xl font-semibold text-gray-700 hover:text-blue-700 transition-all duration-200 mx-auto shadow-sm hover:shadow-md transform hover:-translate-y-0.5 font-serif"
        >
          {{ number }}
        </button>
      </div>

      <!-- 底部按钮 -->
      <div class="flex justify-center space-x-6">
        <button 
          @click="$emit('make-call')"
          class="w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"
        >
          📞
        </button>
        <button 
          @click="$emit('clear-number')"
          class="w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 font-serif"
        >
          🔢
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModalComponents',
  props: {
    showAddModal: Boolean,
    showSettingsModal: Boolean,
    showDeleteModal: Boolean,
    showDialModal: Boolean,
    isEditing: Boolean,
    currentAssistant: Object,
    currentSettingsAssistant: Object,
    currentPersonality: String,
    deleteAssistantName: String,
    dialNumber: String
  },
  emits: [
    'close-add', 'update-name', 'update-description', 'save',
    'close-settings', 'update-personality', 'save-personality',
    'close-delete', 'confirm-delete',
    'close-dial', 'add-number', 'make-call', 'clear-number'
  ]
}
</script>