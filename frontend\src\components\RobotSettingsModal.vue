<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    @click.self="$emit('close')"
  >
    <div class="bg-white rounded-2xl shadow-2xl w-[1200px] h-[800px] transform transition-all duration-300 scale-100 flex flex-col">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
            ⚙️
          </div>
          <h3 class="text-xl font-bold text-gray-900">{{ robot?.name }} - 机器人设置</h3>
        </div>
        <button 
          @click="$emit('close')"
          class="w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
        >
          ✕
        </button>
      </div>

      <!-- 主体内容 -->
      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧导航 -->
        <div class="w-64 bg-gray-50 border-r border-gray-200 p-4">
          <nav class="space-y-2">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                'w-full text-left px-4 py-3 rounded-lg transition-all duration-200 flex items-center space-x-3',
                activeTab === tab.id 
                  ? 'bg-blue-500 text-white shadow-md' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              ]"
            >
              <span class="text-lg">{{ tab.icon }}</span>
              <span class="font-medium">{{ tab.name }}</span>
            </button>
          </nav>
        </div>

        <!-- 右侧内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
          <!-- 人设与回复逻辑 -->
          <div v-if="activeTab === 'personality'" class="flex-1 p-6 overflow-y-auto">
            <div class="max-w-4xl space-y-8">
              <!-- 人设配置 -->
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">👤</span>
                    人设配置
                  </h4>
                  <button
                    @click="showOptimizeModal = true"
                    class="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-200 shadow-md hover:shadow-lg"
                  >
                    <span class="text-sm">✨</span>
                    <span class="text-sm font-medium">自动优化提示词</span>
                  </button>
                </div>
                <textarea
                  v-model="localPersonality"
                  placeholder="请输入助手的人设配置，例如：你是一个专业的客服助手，性格温和，善于解决问题..."
                  rows="6"
                  class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 bg-gray-50 focus:bg-white"
                ></textarea>
              </div>

              <!-- 回复逻辑配置 -->
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <span class="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">🧠</span>
                  回复逻辑配置
                </h4>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <!-- 系统角色 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">系统角色</label>
                    <textarea 
                      v-model="replyLogic.systemRole"
                      placeholder="你是一个专业的对话大模型工程师..."
                      rows="3"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    ></textarea>
                  </div>

                  <!-- 对话指令 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">对话指令</label>
                    <textarea 
                      v-model="replyLogic.dialogCommand"
                      placeholder="请以简洁、专业的方式回答用户的问题..."
                      rows="3"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    ></textarea>
                  </div>

                  <!-- 回复模式 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">回复模式</label>
                    <select 
                      v-model="replyLogic.responseMode"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="simple">简洁模式</option>
                      <option value="detailed">详细模式</option>
                      <option value="creative">创意模式</option>
                    </select>
                  </div>

                  <!-- 温度 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                      温度 ({{ replyLogic.temperature }})
                    </label>
                    <input 
                      type="range" 
                      v-model.number="replyLogic.temperature"
                      min="0" 
                      max="1" 
                      step="0.1"
                      class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    >
                  </div>

                  <!-- 最大Token数 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">最大Token数</label>
                    <input 
                      type="number" 
                      v-model.number="replyLogic.maxTokens"
                      min="1" 
                      max="4000"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                  </div>

                  <!-- 语言 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">语言</label>
                    <select 
                      v-model="replyLogic.language"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="zh-cn">中文</option>
                      <option value="en">English</option>
                      <option value="ja">日本語</option>
                    </select>
                  </div>

                  <!-- 发音人 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">发音人</label>
                    <select 
                      v-model="replyLogic.speaker"
                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="601002">女声-温柔</option>
                      <option value="601001">男声-沉稳</option>
                      <option value="601003">女声-活泼</option>
                    </select>
                  </div>

                  <!-- 语速 -->
                  <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                      语速 ({{ replyLogic.speechSpeed }})
                    </label>
                    <input 
                      type="range" 
                      v-model.number="replyLogic.speechSpeed"
                      min="0.5" 
                      max="2" 
                      step="0.1"
                      class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 编排（知识库设置） -->
          <div v-if="activeTab === 'orchestration'" class="flex-1 p-6 overflow-y-auto">
            <div class="max-w-4xl">
              <div class="bg-white border border-gray-200 rounded-xl p-6">
                <div class="flex items-center justify-between mb-6">
                  <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                    <span class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">📚</span>
                    知识库设置
                  </h4>
                  <div class="flex items-center space-x-2">
                    <button class="p-2 text-gray-500 hover:text-gray-700 transition-colors">
                      ⚙️
                    </button>
                  </div>
                </div>

                <!-- 召回设置 -->
                <div class="space-y-6">
                  <div>
                    <h5 class="text-md font-semibold text-gray-800 mb-4">召回</h5>
                    
                    <!-- 调用方式 -->
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        调用方式
                        <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                      </label>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input 
                            type="radio" 
                            v-model="knowledgeConfig.callMethod" 
                            value="auto"
                            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          >
                          <span class="ml-2 text-sm text-gray-700">自动调用</span>
                        </label>
                        <label class="flex items-center">
                          <input 
                            type="radio" 
                            v-model="knowledgeConfig.callMethod" 
                            value="manual"
                            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          >
                          <span class="ml-2 text-sm text-gray-700">按需调用</span>
                        </label>
                      </div>
                    </div>

                    <!-- 搜索策略 -->
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        搜索策略
                        <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                      </label>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input 
                            type="radio" 
                            v-model="knowledgeConfig.searchStrategy" 
                            value="mixed"
                            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          >
                          <span class="ml-2 text-sm text-gray-700">混合</span>
                        </label>
                        <label class="flex items-center">
                          <input 
                            type="radio" 
                            v-model="knowledgeConfig.searchStrategy" 
                            value="semantic"
                            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          >
                          <span class="ml-2 text-sm text-gray-700">语义</span>
                        </label>
                        <label class="flex items-center">
                          <input 
                            type="radio" 
                            v-model="knowledgeConfig.searchStrategy" 
                            value="full_text"
                            class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          >
                          <span class="ml-2 text-sm text-gray-700">全文</span>
                        </label>
                      </div>
                    </div>

                    <!-- 最大召回数量 -->
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        最大召回数量
                        <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                      </label>
                      <div class="flex items-center space-x-4">
                        <input 
                          type="range" 
                          v-model.number="knowledgeConfig.maxRecall"
                          min="1" 
                          max="20" 
                          step="1"
                          class="flex-1 h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                        >
                        <div class="w-12 h-8 border border-gray-300 rounded text-center text-sm flex items-center justify-center">
                          {{ knowledgeConfig.maxRecall }}
                        </div>
                      </div>
                    </div>

                    <!-- 最小匹配度 -->
                    <div class="mb-6">
                      <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        最小匹配度
                        <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                      </label>
                      <div class="flex items-center space-x-4">
                        <input 
                          type="range" 
                          v-model.number="knowledgeConfig.minScore"
                          min="0" 
                          max="1" 
                          step="0.01"
                          class="flex-1 h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                        >
                        <div class="w-16 h-8 border border-gray-300 rounded text-center text-sm flex items-center justify-center">
                          {{ knowledgeConfig.minScore.toFixed(2) }}
                        </div>
                      </div>
                    </div>

                    <!-- 开关选项 -->
                    <div class="space-y-4">
                      <div class="flex items-center justify-between">
                        <label class="text-sm font-medium text-gray-700 flex items-center">
                          查询改写
                          <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                        </label>
                        <label class="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            v-model="knowledgeConfig.queryRewrite"
                            class="sr-only peer"
                          >
                          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                        </label>
                      </div>

                      <div class="flex items-center justify-between">
                        <label class="text-sm font-medium text-gray-700 flex items-center">
                          结果重排
                          <span class="ml-2 w-4 h-4 text-gray-400 cursor-help">ℹ️</span>
                        </label>
                        <label class="relative inline-flex items-center cursor-pointer">
                          <input 
                            type="checkbox" 
                            v-model="knowledgeConfig.resultRerank"
                            class="sr-only peer"
                          >
                          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 知识库内容区域 -->
              <div class="mt-6 space-y-4">
                <!-- 知识库选择界面 -->
                <div v-if="showKnowledgeBaseSelection" class="bg-gray-50 border border-gray-200 rounded-xl p-6">
                  <!-- 返回按钮和标题 -->
                  <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                      <button
                        @click="closeKnowledgeBaseSelection"
                        class="mr-3 w-8 h-8 bg-white rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-600 transition-colors"
                      >
                        ←
                      </button>
                      <h4 class="text-lg font-semibold text-gray-900">
                        {{ currentKnowledgeBaseType === 'text' ? '选择文本知识库' :
                           currentKnowledgeBaseType === 'table' ? '选择表格知识库' : '选择照片知识库' }}
                      </h4>
                    </div>
                    <button
                      @click="openCreateKnowledgeBase"
                      class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                    >
                      创建知识库
                    </button>
                  </div>

                  <!-- 搜索和筛选 -->
                  <div class="flex items-center space-x-4 mb-4">
                    <div class="relative flex-1">
                      <input
                        v-model="searchQuery"
                        placeholder="搜索知识库..."
                        class="w-full pl-8 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                      <span class="absolute left-2 top-2.5 text-gray-400">🔍</span>
                    </div>
                  </div>

                  <!-- 知识库列表 -->
                  <div v-if="filteredKnowledgeBases.length > 0" class="space-y-3 max-h-60 overflow-y-auto">
                    <div
                      v-for="kb in filteredKnowledgeBases"
                      :key="kb.id"
                      class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-200"
                    >
                      <div class="flex items-center space-x-3">
                        <div :class="[
                          'w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm',
                          kb.type === 'text' ? 'bg-blue-500' : kb.type === 'table' ? 'bg-green-500' : 'bg-orange-500'
                        ]">
                          {{ kb.type === 'text' ? '📄' : kb.type === 'table' ? '📊' : '🖼️' }}
                        </div>
                        <div>
                          <h5 class="font-semibold text-gray-900 text-sm">{{ kb.name }}</h5>
                          <p class="text-xs text-gray-600">{{ kb.description }}</p>
                          <div class="flex items-center space-x-3 mt-1">
                            <span class="text-xs text-gray-500">{{ kb.segmentCount || 0 }} 个分段</span>
                            <span class="text-xs text-gray-500">{{ formatDate(kb.updateTime) }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="flex space-x-2">
                        <button
                          @click="selectKnowledgeBase(kb)"
                          class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                        >
                          选择
                        </button>
                        <button
                          @click="editKnowledgeBase(kb)"
                          class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                        >
                          编辑
                        </button>
                        <!-- 更多操作下拉菜单 -->
                        <div class="relative">
                          <button
                            @click="toggleKnowledgeBaseMenu(kb.id)"
                            class="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400 transition-colors"
                          >
                            更多
                          </button>
                          <!-- 下拉菜单 -->
                          <div
                            v-if="activeKnowledgeBaseMenu === kb.id"
                            class="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                          >
                            <button
                              @click="viewKnowledgeBaseDetails(kb)"
                              class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"
                            >
                              查看详情
                            </button>
                            <button
                              @click="openKnowledgeBaseMarket"
                              class="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"
                            >
                              资源广场
                            </button>
                            <button
                              @click="confirmDeleteKnowledgeBase(kb)"
                              class="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded-b-lg"
                            >
                              删除知识库
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态 -->
                  <div v-else class="text-center py-8">
                    <div class="w-16 h-16 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                      <span class="text-2xl text-gray-400">📚</span>
                    </div>
                    <h5 class="text-md font-semibold text-gray-900 mb-2">暂无知识库</h5>
                    <p class="text-sm text-gray-600 mb-4">请先创建后添加</p>
                    <button
                      @click="openCreateKnowledgeBase"
                      class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
                    >
                      创建知识库
                    </button>
                  </div>
                </div>

                <!-- 创建知识库界面 -->
                <div v-else-if="showCreateKnowledgeBase" class="bg-gray-50 border border-gray-200 rounded-xl p-6">
                  <!-- 返回按钮和标题 -->
                  <div class="flex items-center mb-6">
                    <button
                      @click="closeCreateKnowledgeBase"
                      class="mr-3 w-8 h-8 bg-white rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-600 transition-colors"
                    >
                      ←
                    </button>
                    <h4 class="text-lg font-semibold text-gray-900">创建知识库</h4>
                  </div>

                  <!-- 知识库创建方式 -->
                  <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-700 mb-3">知识库创建方式</h5>
                    <div class="space-y-2">
                      <!-- 关联火山知识库 -->
                      <div
                        @click="createMethod = 'volcano'"
                        :class="[
                          'p-3 border-2 rounded-lg cursor-pointer transition-all duration-200',
                          createMethod === 'volcano' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                      >
                        <div class="flex items-center space-x-2">
                          <div class="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center text-white text-xs">
                            🏔️
                          </div>
                          <div>
                            <h6 class="font-semibold text-gray-900 text-sm">关联火山知识库</h6>
                            <p class="text-xs text-gray-600">支持文本、表格知识库，精细化切片管理</p>
                          </div>
                        </div>
                      </div>

                      <!-- 创建扣子知识库 -->
                      <div
                        @click="createMethod = 'local'"
                        :class="[
                          'p-3 border-2 rounded-lg cursor-pointer transition-all duration-200',
                          createMethod === 'local' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                      >
                        <div class="flex items-center space-x-2">
                          <div class="w-6 h-6 bg-purple-500 rounded-lg flex items-center justify-center text-white text-xs">
                            👻
                          </div>
                          <div>
                            <h6 class="font-semibold text-gray-900 text-sm">创建扣子知识库</h6>
                            <p class="text-xs text-gray-600">支持图片、文本、表格知识库，智能切片管理</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 知识库类型选择 -->
                  <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-700 mb-3">知识库类型</h5>
                    <div class="grid grid-cols-3 gap-2">
                      <div
                        @click="newKbType = 'text'"
                        :class="[
                          'p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center',
                          newKbType === 'text' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                      >
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1">
                          📄
                        </div>
                        <h6 class="font-semibold text-gray-900 text-xs">文本格式</h6>
                      </div>

                      <div
                        @click="newKbType = 'table'"
                        :class="[
                          'p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center',
                          newKbType === 'table' ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                      >
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1">
                          📊
                        </div>
                        <h6 class="font-semibold text-gray-900 text-xs">表格格式</h6>
                      </div>

                      <div
                        @click="newKbType = 'image'"
                        :class="[
                          'p-3 border-2 rounded-lg cursor-pointer transition-all duration-200 text-center',
                          newKbType === 'image' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                      >
                        <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center text-white text-sm mx-auto mb-1">
                          🖼️
                        </div>
                        <h6 class="font-semibold text-gray-900 text-xs">照片类型</h6>
                      </div>
                    </div>
                  </div>

                  <!-- 基本信息 -->
                  <div class="space-y-3">
                    <div>
                      <label class="block text-xs font-semibold text-gray-700 mb-1">
                        名称 <span class="text-red-500">*</span>
                      </label>
                      <input
                        v-model="newKbName"
                        placeholder="输入数据集名称"
                        class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      >
                      <div class="text-right text-xs text-gray-500 mt-1">{{ newKbName.length }}/100</div>
                    </div>

                    <div>
                      <label class="block text-xs font-semibold text-gray-700 mb-1">描述</label>
                      <textarea
                        v-model="newKbDescription"
                        placeholder="输入数据集内容的描述"
                        rows="2"
                        class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"
                      ></textarea>
                      <div class="text-right text-xs text-gray-500 mt-1">{{ newKbDescription.length }}/2000</div>
                    </div>

                    <div class="flex justify-end space-x-2 pt-2">
                      <button
                        @click="closeCreateKnowledgeBase"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors text-sm"
                      >
                        取消
                      </button>
                      <button
                        @click="createKnowledgeBase"
                        :disabled="!newKbName.trim()"
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
                      >
                        完成创建
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 默认知识库类型展示 -->
                <div v-else>
                  <!-- 文本知识库 -->
                  <div class="bg-white border border-gray-200 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h5 class="text-md font-semibold text-gray-800 flex items-center">
                        <span class="mr-2">📄</span>
                        文本
                      </h5>
                      <button
                        @click="openKnowledgeBaseSelection('text')"
                        class="w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"
                      >
                        +
                      </button>
                    </div>
                    <p class="text-sm text-gray-600">
                      将文档、URL、三方数据源上传为文本知识库后，用户发送消息时，智能体能够引用文本知识中的内容回答用户问题。
                    </p>
                  </div>

                  <!-- 表格知识库 -->
                  <div class="bg-white border border-gray-200 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h5 class="text-md font-semibold text-gray-800 flex items-center">
                        <span class="mr-2">📊</span>
                        表格
                      </h5>
                      <button
                        @click="openKnowledgeBaseSelection('table')"
                        class="w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"
                      >
                        +
                      </button>
                    </div>
                    <p class="text-sm text-gray-600">
                      用户上传表格后，支持按照表格的某列来匹配适合的行给智能体引用，同时也支持基于自然语言对数据库进行查询和计算。
                    </p>
                  </div>

                  <!-- 照片知识库 -->
                  <div class="bg-white border border-gray-200 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h5 class="text-md font-semibold text-gray-800 flex items-center">
                        <span class="mr-2">🖼️</span>
                        照片
                      </h5>
                      <button
                        @click="openKnowledgeBaseSelection('image')"
                        class="w-6 h-6 bg-gray-100 rounded text-gray-600 hover:bg-gray-200 transition-colors"
                      >
                        +
                      </button>
                    </div>
                    <p class="text-sm text-gray-600">
                      照片上传到知识库后自动/手动添加适文描述，智能体可以基于照片的描述匹配到最合适的照片。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 预览与调试 -->
          <div v-if="activeTab === 'preview'" class="flex-1 p-6 overflow-y-auto">
            <div class="max-w-4xl">
              <div class="bg-white border border-gray-200 rounded-xl p-6 h-full">
                <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                  <span class="w-6 h-6 bg-orange-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">🔍</span>
                  预览与调试
                </h4>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                  <!-- 左侧：对话测试 -->
                  <div class="border border-gray-200 rounded-xl p-4">
                    <h5 class="text-md font-semibold text-gray-800 mb-4">人设与回复逻辑测试</h5>
                    
                    <!-- 测试对话区域 -->
                    <div class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4">
                      <div v-for="(message, index) in testMessages" :key="index" class="mb-3">
                        <div :class="[
                          'max-w-xs p-3 rounded-lg text-sm',
                          message.isUser 
                            ? 'bg-blue-500 text-white ml-auto' 
                            : 'bg-white border border-gray-200'
                        ]">
                          {{ message.content }}
                        </div>
                      </div>
                    </div>
                    
                    <!-- 输入框 -->
                    <div class="flex space-x-2">
                      <input 
                        v-model="testInput"
                        @keyup.enter="sendTestMessage"
                        placeholder="输入测试消息..."
                        class="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                      <button
                        @click="sendTestMessage"
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                      >
                        发送
                      </button>
                    </div>
                  </div>

                  <!-- 右侧：预览与调试 -->
                  <div class="border border-gray-200 rounded-xl p-4">
                    <h5 class="text-md font-semibold text-gray-800 mb-4">预览与调试</h5>
                    
                    <!-- 调试信息 -->
                    <div class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto">
                      <div class="text-sm text-gray-600 space-y-2">
                        <div><strong>当前配置:</strong></div>
                        <div>• 人设: {{ localPersonality ? '已配置' : '未配置' }}</div>
                        <div>• 回复模式: {{ replyLogic.responseMode }}</div>
                        <div>• 温度: {{ replyLogic.temperature }}</div>
                        <div>• 最大Token: {{ replyLogic.maxTokens }}</div>
                        <div>• 知识库调用: {{ knowledgeConfig.callMethod }}</div>
                        <div>• 搜索策略: {{ knowledgeConfig.searchStrategy }}</div>
                      </div>
                    </div>
                    
                    <!-- 调试按钮 -->
                    <div class="mt-4 space-y-2">
                      <button
                        @click="exportConfiguration"
                        class="w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                      >
                        导出配置
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50">
        <button 
          @click="$emit('close')"
          class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          取消
        </button>
        <button
          @click="saveSettings"
          class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors relative"
        >
          保存设置
          <!-- 成功提示 -->
          <div
            v-if="saveSuccessMessage"
            class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap shadow-lg"
          >
            ✅ 保存成功！
          </div>
        </button>
      </div>
    </div>

    <!-- 优化提示词模态框 -->
    <div
      v-if="showOptimizeModal"
      class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-60"
      @click.self="closeOptimizeModal"
    >
      <div class="bg-white rounded-2xl shadow-2xl w-[800px] max-h-[90vh] overflow-hidden transform transition-all duration-300 scale-100 flex flex-col">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white text-sm mr-3">
              ✨
            </div>
            <h3 class="text-xl font-bold text-gray-900">自动优化提示词</h3>
          </div>
          <button
            @click="closeOptimizeModal"
            class="w-8 h-8 rounded-lg hover:bg-gray-100 flex items-center justify-center text-gray-500 hover:text-gray-700 transition-colors"
          >
            ✕
          </button>
        </div>

        <!-- 主体内容 -->
        <div class="flex-1 p-6 overflow-y-auto">
          <!-- 优化选项 -->
          <div class="mb-6">
            <div class="flex space-x-4 mb-4">
              <button
                @click="optimizeMode = 'auto'"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-all duration-200',
                  optimizeMode === 'auto'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                自动优化
              </button>
              <button
                @click="optimizeMode = 'custom'"
                :class="[
                  'px-4 py-2 rounded-lg font-medium transition-all duration-200',
                  optimizeMode === 'custom'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                根据调试结果优化
              </button>
            </div>
          </div>

          <!-- 优化输入区域 -->
          <div class="mb-6">
            <div class="flex items-center mb-3">
              <span class="text-lg mr-2">✨</span>
              <label class="text-sm font-medium text-gray-700">你希望如何编写或优化提示词？</label>
            </div>
            <div class="relative">
              <textarea
                v-model="optimizeInput"
                placeholder="请描述您希望如何优化提示词，例如：让助手更专业、更友善、更具创意..."
                rows="4"
                class="w-full px-4 py-3 pr-12 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-all duration-200 bg-blue-50/30 focus:bg-white"
              ></textarea>
              <button
                @click="optimizePrompt"
                :disabled="!optimizeInput.trim() || isOptimizing"
                class="absolute right-3 bottom-3 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
              >
                <span v-if="!isOptimizing">→</span>
                <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </button>
            </div>
          </div>

          <!-- 优化结果显示 -->
          <div v-if="optimizedResult" class="mb-6">
            <h5 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <span class="mr-2">🎯</span>
              优化结果
            </h5>
            <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-4">
              <div class="text-sm text-gray-700 whitespace-pre-wrap">{{ optimizedResult }}</div>
            </div>
          </div>

          <!-- 当前人设显示 -->
          <div class="mb-6">
            <h5 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <span class="mr-2">📝</span>
              当前人设
            </h5>
            <div class="bg-gray-50 border border-gray-200 rounded-xl p-4">
              <div class="text-sm text-gray-700 whitespace-pre-wrap">{{ localPersonality || '暂无人设配置' }}</div>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="flex items-center justify-end space-x-4 p-6 border-t border-gray-200 bg-gray-50">
          <button
            @click="closeOptimizeModal"
            class="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            取消
          </button>
          <button
            @click="applyOptimizedResult"
            :disabled="!optimizedResult"
            class="px-6 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg hover:from-purple-600 hover:to-blue-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200"
          >
            应用优化结果
          </button>
        </div>
      </div>
    </div>

    <!-- 知识库管理器 -->
    <KnowledgeBaseManager
      :show="showKnowledgeBaseManager"
      :knowledgeBase="selectedKnowledgeBaseForEdit"
      @close="showKnowledgeBaseManager = false"
      @complete="onKnowledgeBaseManagerComplete"
    />

    <!-- 知识库编辑器 -->
    <KnowledgeBaseEditor
      :show="showKnowledgeBaseEditor"
      :knowledgeBase="selectedKnowledgeBaseForEdit"
      @close="showKnowledgeBaseEditor = false"
      @save="onKnowledgeBaseEditorSave"
    />

    <!-- 知识库资源广场 -->
    <div
      v-if="showKnowledgeBaseMarket"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="closeKnowledgeBaseMarket"
    >
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <!-- 标题栏 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-xl font-bold text-gray-900">知识库资源广场</h3>
          <button
            @click="closeKnowledgeBaseMarket"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- 内容区域 -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <!-- 搜索栏 -->
          <div class="mb-6">
            <div class="relative">
              <input
                v-model="marketSearchQuery"
                type="text"
                placeholder="搜索知识库资源..."
                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
              <span class="absolute left-3 top-3.5 text-gray-400">🔍</span>
            </div>
          </div>

          <!-- 分类标签 -->
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              @click="marketSelectedType = ''"
              :class="[
                'px-4 py-2 rounded-full text-sm transition-colors',
                marketSelectedType === '' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              全部
            </button>
            <button
              @click="marketSelectedType = 'text'"
              :class="[
                'px-4 py-2 rounded-full text-sm transition-colors',
                marketSelectedType === 'text' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              文本文档
            </button>
            <button
              @click="marketSelectedType = 'table'"
              :class="[
                'px-4 py-2 rounded-full text-sm transition-colors',
                marketSelectedType === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              表格数据
            </button>
            <button
              @click="marketSelectedType = 'image'"
              :class="[
                'px-4 py-2 rounded-full text-sm transition-colors',
                marketSelectedType === 'image' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              图片资源
            </button>
          </div>

          <!-- 资源列表 -->
          <div v-if="filteredMarketKnowledgeBases.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="kb in filteredMarketKnowledgeBases"
              :key="kb.id"
              class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center mb-3">
                <div :class="[
                  'w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg',
                  kb.type === 'text' ? 'bg-blue-500' :
                  kb.type === 'table' ? 'bg-green-500' : 'bg-orange-500'
                ]">
                  {{ kb.icon }}
                </div>
                <div class="ml-3">
                  <h4 class="font-semibold text-gray-900 text-sm">{{ kb.name }}</h4>
                  <p class="text-xs text-gray-500">{{ getKnowledgeBaseTypeLabel(kb.type) }}</p>
                </div>
              </div>
              <p class="text-sm text-gray-600 mb-3">{{ kb.description }}</p>
              <div class="flex items-center justify-between">
                <span class="text-xs text-gray-500">{{ kb.segmentCount || 0 }} 个分段</span>
                <div class="flex space-x-2">
                  <button
                    @click="selectKnowledgeBaseFromMarket(kb)"
                    class="px-3 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors"
                  >
                    选择
                  </button>
                  <button
                    @click="importKnowledgeBaseFromMarket(kb)"
                    class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
                  >
                    导入
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-2xl text-gray-400">📚</span>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无知识库资源</h3>
            <p class="text-gray-500">您还没有创建任何知识库，请先创建知识库</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div
      v-if="showDeleteConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="cancelDeleteKnowledgeBase"
    >
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-md p-6">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">确认删除</h3>
            <p class="text-sm text-gray-600">此操作不可撤销</p>
          </div>
        </div>

        <p class="text-gray-700 mb-6">
          您确定要删除知识库 <strong>"{{ knowledgeBaseToDelete?.name }}"</strong> 吗？
          <br>
          <span class="text-sm text-gray-500">删除后将无法恢复，包含的所有内容都将丢失。</span>
        </p>

        <div class="flex space-x-3">
          <button
            @click="cancelDeleteKnowledgeBase"
            class="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
          >
            取消
          </button>
          <button
            @click="deleteKnowledgeBase"
            class="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            确认删除
          </button>
        </div>
      </div>
    </div>
  </div>


</template>

<script>
import KnowledgeBaseManager from './KnowledgeBaseManager.vue'
import KnowledgeBaseEditor from './KnowledgeBaseEditor.vue'

export default {
  name: 'RobotSettingsModal',
  components: {
    KnowledgeBaseManager,
    KnowledgeBaseEditor
  },
  props: {
    show: Boolean,
    robot: Object
  },
  emits: ['close', 'save'],
  data() {
    return {
      activeTab: 'personality',
      localPersonality: '',
      testInput: '',
      showOptimizeModal: false,
      optimizeMode: 'auto',
      optimizeInput: '',
      optimizedResult: '',
      isOptimizing: false,
      showKnowledgeBaseSelection: false,
      showCreateKnowledgeBase: false,
      showKnowledgeBaseManager: false,
      showKnowledgeBaseEditor: false,
      currentKnowledgeBaseType: '',
      searchQuery: '',
      createMethod: 'local', // 'volcano' | 'local'
      newKbType: 'text', // 'text' | 'table' | 'image'
      newKbName: '',
      newKbDescription: '',
      selectedKnowledgeBaseForEdit: null,
      activeKnowledgeBaseMenu: null, // 当前激活的更多菜单
      showKnowledgeBaseMarket: false, // 显示知识库资源广场
      showDeleteConfirm: false, // 显示删除确认对话框
      knowledgeBaseToDelete: null, // 待删除的知识库
      saveSuccessMessage: false, // 显示保存成功提示
      // 真实知识库数据（从API获取）
      knowledgeBases: [],
      // 知识库资源广场相关
      marketSearchQuery: '',
      marketSelectedType: '',
      testMessages: [
        {
          content: '你好，我想了解一下你的功能',
          isUser: true
        },
        {
          content: '您好！我是您的AI助手，我可以帮助您解答问题、提供信息和进行对话。请告诉我您需要什么帮助？',
          isUser: false
        }
      ],
      tabs: [
        { id: 'personality', name: '人设与回复逻辑', icon: '👤' },
        { id: 'orchestration', name: '编排', icon: '📚' },
        { id: 'preview', name: '预览与调试', icon: '🔍' }
      ],
      replyLogic: {
        systemRole: '你是一个专业的对话大模型工程师，专注于帮助用户解决技术相关的问题。',
        dialogCommand: '请以简洁、专业的方式回答用户的问题，尽量控制在合理的长度内',
        responseMode: 'simple',
        temperature: 0.6,
        maxTokens: 50,
        language: 'zh-cn',
        speaker: '601002',
        speechSpeed: 1.0
      },
      knowledgeConfig: {
        callMethod: 'auto',
        searchStrategy: 'mixed',
        maxRecall: 5,
        minScore: 0.50,
        queryRewrite: true,
        resultRerank: true
      }
    }
  },
  computed: {
    filteredKnowledgeBases() {
      let filtered = this.knowledgeBases

      // 按类型筛选
      if (this.currentKnowledgeBaseType) {
        filtered = filtered.filter(kb => kb.type === this.currentKnowledgeBaseType)
      }

      // 按搜索关键词筛选
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(kb =>
          kb.name.toLowerCase().includes(query) ||
          kb.description.toLowerCase().includes(query)
        )
      }

      return filtered
    },

    filteredMarketKnowledgeBases() {
      let filtered = this.knowledgeBases

      // 按类型筛选
      if (this.marketSelectedType) {
        filtered = filtered.filter(kb => kb.type === this.marketSelectedType)
      }

      // 按搜索关键词筛选
      if (this.marketSearchQuery.trim()) {
        const query = this.marketSearchQuery.toLowerCase()
        filtered = filtered.filter(kb =>
          kb.name.toLowerCase().includes(query) ||
          kb.description.toLowerCase().includes(query)
        )
      }

      return filtered
    }
  },
  watch: {
    robot: {
      handler(newRobot) {
        if (newRobot) {
          this.loadRobotSettings()
        }
      },
      immediate: true
    },
    show(newVal) {
      if (newVal && this.robot) {
        this.loadRobotSettings()
      }
    }
  },
  methods: {
    loadRobotSettings() {
      if (!this.robot) return

      // 加载人设配置
      this.localPersonality = this.robot.personnel_design || ''

      // 加载回复逻辑配置
      if (this.robot.reply_logic) {
        try {
          const replyLogicData = JSON.parse(this.robot.reply_logic)
          this.replyLogic = { ...this.replyLogic, ...replyLogicData }
        } catch (e) {
          console.warn('解析回复逻辑配置失败:', e)
        }
      }

      // 加载知识库配置
      if (this.robot.knowledge_config) {
        try {
          const knowledgeConfigData = JSON.parse(this.robot.knowledge_config)
          this.knowledgeConfig = { ...this.knowledgeConfig, ...knowledgeConfigData }
        } catch (e) {
          console.warn('解析知识库配置失败:', e)
        }
      }
    },

    async saveSettings() {
      if (!this.robot) return

      try {
        const response = await fetch('http://localhost:8081/api/robot/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: this.robot.id,
            name: this.robot.name,
            description: this.robot.description,
            personnel_design: this.localPersonality,
            reply_logic: JSON.stringify(this.replyLogic),
            knowledge_config: JSON.stringify(this.knowledgeConfig)
          })
        })

        const result = await response.json()
        if (result.code === 200) {
          this.$emit('save', {
            ...this.robot,
            personnel_design: this.localPersonality,
            reply_logic: JSON.stringify(this.replyLogic),
            knowledge_config: JSON.stringify(this.knowledgeConfig)
          })

          // 显示成功提示，但不关闭模态框，保留用户输入的内容
          this.showSuccessMessage()

          // 在控制台记录成功信息
          console.log('机器人设置保存成功:', this.robot.name)
        } else {
          throw new Error(result.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存设置失败:', error)
        alert('保存失败: ' + error.message)
      }
    },

    showSuccessMessage() {
      // 显示成功提示
      this.saveSuccessMessage = true
      // 3秒后自动隐藏
      setTimeout(() => {
        this.saveSuccessMessage = false
      }, 3000)
    },

    async loadKnowledgeBases() {
      try {
        const response = await fetch('http://localhost:8081/api/knowledge-base/list', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({})
        })

        const result = await response.json()
        if (result.code === 200) {
          this.knowledgeBases = result.data.map(kb => ({
            ...kb,
            type: this.mapKnowledgeBaseType(kb.type), // 映射类型
            icon: this.getKnowledgeBaseIcon(kb.type),
            updateTime: new Date(kb.updatedAt)
          }))
          console.log('知识库列表加载成功:', this.knowledgeBases)
        } else {
          console.error('获取知识库列表失败:', result.msg)
        }
      } catch (error) {
        console.error('获取知识库列表失败:', error)
        // 如果API调用失败，使用默认的空数组
        this.knowledgeBases = []
      }
    },

    getKnowledgeBaseIcon(type) {
      const icons = {
        'text': '📄',
        'table': '📊',
        'image': '🖼️',
        'document': '📄'
      }
      return icons[type] || '📄'
    },

    // 将后端的知识库类型映射到前端类型
    mapKnowledgeBaseType(backendType) {
      const typeMapping = {
        'document': 'text',  // 后端的document类型映射为前端的text类型
        'text': 'text',
        'table': 'table',
        'image': 'image'
      }
      return typeMapping[backendType] || 'text'
    },

    async sendTestMessage() {
      if (!this.testInput.trim()) return

      const userMessage = this.testInput.trim()

      // 添加用户消息
      this.testMessages.push({
        content: userMessage,
        isUser: true
      })

      this.testInput = ''

      try {
        // 调用后端API进行对话
        const response = await fetch('http://localhost:8081/api/llm/communication', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: this.robot.id ? this.robot.id.toString() : 'test_user',
            message: {
              role: "user",
              content: userMessage
            },
            streaming: false
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
          // 添加AI回复
          this.testMessages.push({
            content: result.data,
            isUser: false
          })
        } else {
          throw new Error(result.msg || '服务器返回错误')
        }
      } catch (error) {
        console.error('发送测试消息失败:', error)
        // 添加错误消息
        this.testMessages.push({
          content: '抱歉，测试服务暂时不可用，请稍后重试。错误信息：' + error.message,
          isUser: false
        })
      }
    },

    generateTestResponse() {
      // 简单的测试回复生成逻辑
      const responses = [
        '根据您的问题，我来为您详细解答...',
        '这是一个很好的问题，让我来帮您分析一下...',
        '基于当前的配置，我的回复是...',
        '感谢您的提问，我会根据设定的人设来回答...'
      ]

      return responses[Math.floor(Math.random() * responses.length)] +
             `（温度: ${this.replyLogic.temperature}, 模式: ${this.replyLogic.responseMode}）`
    },



    closeOptimizeModal() {
      this.showOptimizeModal = false
      this.optimizeInput = ''
      this.optimizedResult = ''
      this.optimizeMode = 'auto'
      this.isOptimizing = false
    },

    async optimizePrompt() {
      if (!this.optimizeInput.trim()) return

      this.isOptimizing = true

      try {
        // 构建优化请求
        const optimizeRequest = {
          mode: this.optimizeMode,
          input: this.optimizeInput.trim(),
          currentPersonality: this.localPersonality,
          replyLogic: this.replyLogic
        }

        const response = await fetch('http://localhost:8081/api/prompt/optimize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(optimizeRequest)
        })

        const result = await response.json()
        if (result.code === 200) {
          this.optimizedResult = result.data
        } else {
          // 如果后端接口不可用，使用模拟优化结果
          this.optimizedResult = this.generateMockOptimizedResult()
        }
      } catch (error) {
        console.error('优化提示词失败:', error)
        // 使用模拟优化结果
        this.optimizedResult = this.generateMockOptimizedResult()
      } finally {
        this.isOptimizing = false
      }
    },

    generateMockOptimizedResult() {
      const basePrompts = {
        professional: '你是一位经验丰富的专业顾问，具备深厚的行业知识和丰富的实践经验。你善于分析问题的本质，能够提供准确、实用的解决方案。在与用户交流时，你保持专业而友善的态度，用清晰简洁的语言解释复杂概念，确保用户能够理解并应用你的建议。',
        friendly: '你是一位温暖友善的AI助手，总是以积极乐观的态度帮助用户。你善于倾听，能够理解用户的需求和情感，并给予贴心的回应。你的语言风格亲切自然，让用户感到舒适和被理解。你会耐心地解答问题，并在适当的时候给予鼓励和支持。',
        creative: '你是一位富有创意和想象力的AI助手，擅长从多个角度思考问题，提供新颖独特的解决方案。你善于运用比喻、故事和生动的例子来解释概念，让交流变得有趣而富有启发性。你鼓励用户跳出常规思维，探索更多可能性。',
        technical: '你是一位技术专家，精通各种技术领域的知识。你能够深入浅出地解释复杂的技术概念，提供准确的技术指导和最佳实践建议。你关注技术的实用性和可操作性，能够根据用户的技术水平调整解释的深度和方式。'
      }

      const input = this.optimizeInput.toLowerCase()
      let selectedPrompt = basePrompts.professional

      if (input.includes('友善') || input.includes('温和') || input.includes('亲切')) {
        selectedPrompt = basePrompts.friendly
      } else if (input.includes('创意') || input.includes('创新') || input.includes('想象')) {
        selectedPrompt = basePrompts.creative
      } else if (input.includes('技术') || input.includes('专业') || input.includes('技能')) {
        selectedPrompt = basePrompts.technical
      }

      return `${selectedPrompt}\n\n根据您的要求"${this.optimizeInput}"，我已经为您优化了人设配置，使其更加符合您的期望。这个配置将帮助AI助手更好地理解其角色定位，并以相应的方式与用户互动。`
    },

    applyOptimizedResult() {
      if (this.optimizedResult) {
        this.localPersonality = this.optimizedResult
        this.closeOptimizeModal()
      }
    },

    // 知识库相关方法
    async openKnowledgeBaseSelection(type) {
      this.currentKnowledgeBaseType = type
      this.showKnowledgeBaseSelection = true
      this.showCreateKnowledgeBase = false
      this.searchQuery = ''
      // 加载最新的知识库列表
      await this.loadKnowledgeBases()
    },

    closeKnowledgeBaseSelection() {
      this.showKnowledgeBaseSelection = false
      this.currentKnowledgeBaseType = ''
      this.searchQuery = ''
    },

    openCreateKnowledgeBase() {
      // 直接进入创建知识库模式，不依赖于知识库选择流程
      this.showCreateKnowledgeBase = true
      this.showKnowledgeBaseSelection = false
      // 清除当前知识库类型，表示这是独立的创建操作
      this.currentKnowledgeBaseType = ''
    },

    closeCreateKnowledgeBase() {
      this.showCreateKnowledgeBase = false
      // 如果是从知识库选择页面进入的，返回选择页面
      if (this.currentKnowledgeBaseType) {
        this.showKnowledgeBaseSelection = true
      }
      // 如果是独立创建操作，直接关闭
    },

    selectKnowledgeBase(knowledgeBase) {
      console.log('选择知识库:', knowledgeBase)
      // 这里可以添加将知识库关联到当前机器人的逻辑
      alert(`已选择知识库: ${knowledgeBase.name}`)
      this.closeKnowledgeBaseSelection()
    },

    createKnowledgeBase() {
      if (!this.newKbName.trim()) return

      const newKb = {
        id: Date.now(),
        name: this.newKbName,
        description: this.newKbDescription,
        type: this.newKbType,
        method: this.createMethod,
        itemCount: 0,
        updateTime: new Date(),
        icon: this.newKbType === 'text' ? '📄' : this.newKbType === 'table' ? '📊' : '🖼️'
      }

      // 打开知识库管理器进行详细配置
      this.selectedKnowledgeBaseForEdit = newKb
      this.showKnowledgeBaseManager = true
      this.showCreateKnowledgeBase = false
    },

    editKnowledgeBase(kb) {
      this.selectedKnowledgeBaseForEdit = kb
      this.showKnowledgeBaseEditor = true
    },

    onKnowledgeBaseManagerComplete(result) {
      // 添加到知识库列表
      this.knowledgeBases.push(result)
      console.log('创建知识库完成:', result)

      // 重置表单并关闭界面
      this.resetKnowledgeBaseForm()
      this.showKnowledgeBaseManager = false
      this.closeKnowledgeBaseSelection()
    },

    onKnowledgeBaseEditorSave(result) {
      // 更新知识库信息
      const index = this.knowledgeBases.findIndex(kb => kb.id === result.knowledgeBase.id)
      if (index !== -1) {
        this.knowledgeBases[index] = { ...this.knowledgeBases[index], ...result.knowledgeBase }
      }
      console.log('知识库编辑保存:', result)
      this.showKnowledgeBaseEditor = false
    },

    resetKnowledgeBaseForm() {
      this.newKbName = ''
      this.newKbDescription = ''
      this.newKbType = 'text'
      this.createMethod = 'local'
    },

    formatDate(date) {
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      return date.toLocaleDateString()
    },

    // 新增的知识库管理方法
    toggleKnowledgeBaseMenu(kbId) {
      this.activeKnowledgeBaseMenu = this.activeKnowledgeBaseMenu === kbId ? null : kbId
    },

    viewKnowledgeBaseDetails(kb) {
      // 查看知识库详情
      console.log('查看知识库详情:', kb)
      alert(`知识库详情：\n名称：${kb.name}\n描述：${kb.description}\n类型：${kb.type}\n内容数量：${kb.itemCount}\n更新时间：${this.formatDate(kb.updateTime)}`)
      this.activeKnowledgeBaseMenu = null
    },

    async openKnowledgeBaseMarket() {
      // 打开知识库资源广场
      this.showKnowledgeBaseMarket = true
      this.activeKnowledgeBaseMenu = null
      // 加载最新的知识库列表
      await this.loadKnowledgeBases()
      // 重置筛选条件
      this.marketSearchQuery = ''
      this.marketSelectedType = ''
    },

    closeKnowledgeBaseMarket() {
      this.showKnowledgeBaseMarket = false
      this.marketSearchQuery = ''
      this.marketSelectedType = ''
    },

    getKnowledgeBaseTypeLabel(type) {
      const labels = {
        'text': '文本文档',
        'table': '表格数据',
        'image': '图片资源'
      }
      return labels[type] || '未知类型'
    },

    selectKnowledgeBaseFromMarket(kb) {
      // 选择知识库功能
      console.log('选择知识库:', kb)

      // 根据当前的知识库类型选择模式，添加到对应的配置中
      if (this.currentKnowledgeBaseType) {
        // 如果是从特定类型选择页面进入的，直接添加到对应类型
        this.addKnowledgeBaseToConfig(kb, this.currentKnowledgeBaseType)
      } else {
        // 如果是从资源广场直接选择，根据知识库类型添加
        this.addKnowledgeBaseToConfig(kb, kb.type)
      }

      this.closeKnowledgeBaseMarket()
      alert(`已选择知识库：${kb.name}`)
    },

    importKnowledgeBaseFromMarket(kb) {
      // 导入知识库功能
      console.log('导入知识库:', kb)

      // 这里可以实现复制知识库的功能
      // 暂时先显示提示
      alert(`导入功能开发中，知识库：${kb.name}`)
    },

    addKnowledgeBaseToConfig(kb, type) {
      // 将知识库添加到机器人配置中
      if (!this.knowledgeConfig[type]) {
        this.knowledgeConfig[type] = []
      }

      // 检查是否已经添加过
      const exists = this.knowledgeConfig[type].some(item => item.id === kb.id)
      if (!exists) {
        this.knowledgeConfig[type].push({
          id: kb.id,
          name: kb.name,
          description: kb.description,
          segmentCount: kb.segmentCount || 0
        })
        console.log(`知识库 ${kb.name} 已添加到 ${type} 配置中`)
      } else {
        alert('该知识库已经添加过了')
      }
    },



    exportConfiguration() {
      // 导出配置功能
      try {
        const config = {
          robotName: this.robotName,
          personality: this.localPersonality,
          replyLogic: this.replyLogic,
          knowledgeConfig: this.knowledgeConfig,
          exportTime: new Date().toISOString(),
          version: '1.0'
        }

        const configJson = JSON.stringify(config, null, 2)
        const blob = new Blob([configJson], { type: 'application/json' })
        const url = URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = url
        link.download = `robot_config_${this.robotName || 'unnamed'}_${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        URL.revokeObjectURL(url)

        alert('配置已导出成功！')
      } catch (error) {
        console.error('导出配置失败:', error)
        alert('导出失败: ' + error.message)
      }
    },

    confirmDeleteKnowledgeBase(kb) {
      // 确认删除知识库
      this.knowledgeBaseToDelete = kb
      this.showDeleteConfirm = true
      this.activeKnowledgeBaseMenu = null
    },

    deleteKnowledgeBase() {
      // 执行删除操作
      if (this.knowledgeBaseToDelete) {
        const index = this.knowledgeBases.findIndex(kb => kb.id === this.knowledgeBaseToDelete.id)
        if (index !== -1) {
          this.knowledgeBases.splice(index, 1)
          console.log('删除知识库:', this.knowledgeBaseToDelete.name)
        }
      }
      this.showDeleteConfirm = false
      this.knowledgeBaseToDelete = null
    },

    cancelDeleteKnowledgeBase() {
      this.showDeleteConfirm = false
      this.knowledgeBaseToDelete = null
    },

    handleGlobalClick(event) {
      // 如果点击的不是菜单按钮或菜单内容，则关闭菜单
      if (!event.target.closest('.relative')) {
        this.activeKnowledgeBaseMenu = null
      }
    }
  },

  async mounted() {
    // 添加全局点击监听器，用于关闭下拉菜单
    document.addEventListener('click', this.handleGlobalClick)
    // 加载知识库列表
    await this.loadKnowledgeBases()
  },

  beforeUnmount() {
    // 移除全局点击监听器
    document.removeEventListener('click', this.handleGlobalClick)
  }
}
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 开关样式优化 */
input:checked ~ .peer-checked\:bg-green-500 {
  background-color: #10b981;
}
</style>
