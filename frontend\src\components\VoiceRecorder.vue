<template>
  <div class="voice-recorder">
    <!-- WebRTC通话按钮 -->
    <button
      @click="toggleWebRTCCall"
      :class="webrtcCallClass"
      class="px-6 py-3 rounded-xl transition-all duration-200 font-serif flex items-center"
    >
      <span class="mr-2">{{ isWebRTCConnected ? '📞' : '📱' }}</span>
      {{ isWebRTCConnected ? '挂断通话' : '语音通话' }}
    </button>
    
    <!-- WebRTC连接状态指示 -->
    <div v-if="isWebRTCConnected" class="mt-2 text-sm text-green-600">
      通话中...
    </div>
  </div>
</template>

<script>
export default {
  name: 'VoiceRecorder',
  emits: ['voice-message'],
  data() {
    return {
      currentAssistantId: null,
      // WebRTC相关
      isWebRTCConnected: false,
      webrtcSocket: null,
      peerConnection: null,
      pendingCandidates: [],
      localStream: null,
      lastAsrResult: null,
      isTTSPlaying: false  // 添加TTS播放状态
    }
  },
  computed: {
    webrtcCallClass() {
      return this.isWebRTCConnected
        ? 'bg-red-600/80 hover:bg-red-700/90 text-white animate-pulse backdrop-blur-sm border border-white/20'
        : 'bg-gradient-to-r from-purple-500/80 via-pink-500/80 via-blue-500/80 via-indigo-500/80 to-violet-500/80 hover:from-purple-600/90 hover:via-pink-600/90 hover:via-blue-600/90 hover:via-indigo-600/90 hover:to-violet-600/90 text-white backdrop-blur-sm border border-white/20'
    }
  },
  methods: {
    // WebRTC通话切换
    async toggleWebRTCCall() {
      if (this.isWebRTCConnected) {
        this.disconnectWebRTC()
      } else {
        await this.connectWebRTC()
      }
    },

    // 连接WebRTC
    async connectWebRTC() {
      const currentAssistant = this.$parent.currentSelectedAssistant
      if (!currentAssistant) {
        alert('请先选择一个助手')
        return
      }

      try {
        console.log('[WebRTC] 开始连接...')
        this.webrtcSocket = new WebSocket('ws://localhost:8080/ws')

        this.webrtcSocket.onopen = async () => {
          console.log('[WebSocket] 已连接')
          await this.initWebRTC()
        }

        this.webrtcSocket.onmessage = async (event) => {
          const data = JSON.parse(event.data)
          await this.handleWebRTCMessage(data)
        }

        this.webrtcSocket.onerror = (error) => {
          console.error('[WebSocket] 连接出错:', error)
          this.isWebRTCConnected = false
        }

        this.webrtcSocket.onclose = () => {
          console.log('[WebSocket] 连接关闭')
          this.isWebRTCConnected = false
        }

      } catch (error) {
        console.error('[WebRTC] 连接失败:', error)
        alert('WebRTC连接失败，请检查网络')
      }
    },

    // 初始化WebRTC
    async initWebRTC() {
      try {
        // 1. 创建 RTCPeerConnection
        this.peerConnection = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' }
          ]
        })

        // 2. 获取麦克风音频
        this.localStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
          }
        })
        
        this.localStream.getTracks().forEach(track => {
          this.peerConnection.addTrack(track, this.localStream)
        })

        // 3. 收集 ICE 候选
        this.peerConnection.onicecandidate = (event) => {
          if (event.candidate && this.webrtcSocket) {
            this.webrtcSocket.send(JSON.stringify({
              type: 'ice-candidate',
              candidate: event.candidate
            }))
          }
        }

        // 4. 处理远端音频流
        this.peerConnection.ontrack = (event) => {
          const remoteAudio = new Audio()
          remoteAudio.srcObject = event.streams[0]
          remoteAudio.play().catch(err => {
            console.error('[WebRTC] 播放远端音频失败:', err)
          })
        }

        // 5. 连接状态监听
        this.peerConnection.onconnectionstatechange = () => {
          switch (this.peerConnection.connectionState) {
            case 'connected':
              console.log('[WebRTC] 已连接')
              this.isWebRTCConnected = true
              break
            case 'disconnected':
            case 'failed':
            case 'closed':
              console.log('[WebRTC] 连接关闭/失败')
              this.isWebRTCConnected = false
              break
          }
        }

        // 6. 创建 offer
        const offer = await this.peerConnection.createOffer()
        await this.peerConnection.setLocalDescription(offer)

        // 7. 发送 offer
        this.webrtcSocket.send(JSON.stringify({
          type: 'offer',
          sdp: offer.sdp
        }))

      } catch (error) {
        console.error('[WebRTC] 初始化失败:', error)
        alert('无法访问麦克风，请检查权限设置')
      }
    },

    // 处理WebRTC消息
    async handleWebRTCMessage(data) {
      switch (data.type) {
        case 'answer':
          if (this.peerConnection && data.sdp) {
            const remoteDesc = new RTCSessionDescription({
              type: 'answer',
              sdp: data.sdp,
            })
            await this.peerConnection.setRemoteDescription(remoteDesc)
            console.log('[WebRTC] 已设置远端 SDP answer')

            // 处理缓存的 ICE 候选
            for (const candidate of this.pendingCandidates) {
              try {
                await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate))
                console.log('[WebRTC] 添加缓存 ICE 候选成功')
              } catch (err) {
                console.error('[WebRTC] 添加缓存 ICE 候选失败:', err)
              }
            }
            this.pendingCandidates = []
          }
          break

        case 'asrFinal':
          // 用户的ASR识别结果
          if (data.text && data.text !== '你好 - ') {
            const appComponent = this.findAppComponent();
            if (appComponent) {
              appComponent.addMessage({
                id: Date.now(),
                content: data.text,
                isUser: true,
                messageType: 'voice', // 标记为语音消息
                timestamp: new Date()
              });
            }
          }
          break

        case 'aiResponse':
          // AI的回复
          if (data.text) {
            const appComponent = this.findAppComponent();
            if (appComponent) {
              appComponent.addMessage({
                id: Date.now() + 1,
                content: data.text,
                isUser: false,
                messageType: 'voice', // 标记为语音回复
                timestamp: new Date()
              });
            }
          }
          break

        case 'ice-candidate':
          if (this.peerConnection) {
            const candidate = new RTCIceCandidate(data.candidate)

            if (this.peerConnection.remoteDescription && this.peerConnection.remoteDescription.type) {
              try {
                await this.peerConnection.addIceCandidate(candidate)
                console.log('[WebRTC] 添加 ICE 候选成功')
              } catch (err) {
                console.error('[WebRTC] 添加 ICE 候选失败:', err)
              }
            } else {
              this.pendingCandidates.push(data.candidate)
              console.log('[WebRTC] 缓存 ICE 候选，等待 remoteDescription 设置')
            }
          }
          break
      }
    },

    // 断开WebRTC连接
    disconnectWebRTC() {
      console.log('[WebRTC] 断开连接...')
      
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop())
        this.localStream = null
      }

      if (this.peerConnection) {
        this.peerConnection.close()
        this.peerConnection = null
      }

      if (this.webrtcSocket) {
        this.webrtcSocket.close()
        this.webrtcSocket = null
      }

      this.isWebRTCConnected = false
      this.pendingCandidates = []
      
      // 通话结束时保存对话历史
      const appComponent = this.findAppComponent();
      if (appComponent) {
        appComponent.saveConversation();
      }
    },

    // 查找App组件实例
    findAppComponent() {
      let parent = this.$parent;
      while (parent) {
        if (parent.$options.name === 'App') {
          return parent;
        }
        parent = parent.$parent;
      }
      return null;
    }
  },
  
  beforeUnmount() {
    // 清理WebRTC连接
    this.disconnectWebRTC()
  }
}
</script>
