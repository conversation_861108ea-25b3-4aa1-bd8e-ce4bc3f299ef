<template>
  <div 
    v-if="show" 
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-in fade-in duration-300"
  >
    <!-- 错误提示 Toast -->
    <div v-if="errorMessage" class="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top max-w-xs">
      {{ errorMessage }}
      <div v-if="errorDetails" class="text-xs mt-1 text-red-100 whitespace-pre-wrap">{{ errorDetails }}</div>
    </div>
    
    <!-- 警告提示 Toast -->
    <div v-if="warningMessage" class="fixed top-4 right-4 bg-amber-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-top max-w-xs">
      ⚠️ {{ warningMessage }}
      <div v-if="warningDetails" class="text-xs mt-1 text-amber-100 whitespace-pre-wrap">{{ warningDetails }}</div>
    </div>
    
    <!-- 主容器 -->
    <div class="bg-white rounded-3xl shadow-2xl w-[90vw] max-w-2xl mx-4 overflow-hidden transform transition-all duration-500 hover:shadow-3xl">
      <!-- 头部区域 -->
      <div class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-6 overflow-hidden">
        <div class="absolute -right-20 -top-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
        <div class="absolute left-20 bottom-0 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
        
        <div class="flex items-center justify-between relative z-10">
          <h3 class="text-2xl font-serif font-semibold flex items-center">
            <span class="mr-3 text-2xl">🎵</span>
            声音配置中心
          </h3>
          <button 
            @click="$emit('close')"
            class="text-white/80 hover:text-white transition-colors p-2 rounded-full hover:bg-white/10 hover:scale-110 transition-transform"
            aria-label="关闭"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="p-8">
        <!-- 语音源选项卡 -->
        <div class="mb-8 relative">
          <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-100"></div>
          <button 
            @click="activeTab = 'default'" 
            :class="activeTab === 'default' ? 'text-indigo-600' : 'text-gray-500 hover:text-gray-700'"
            class="py-3 px-6 font-medium relative transition-all text-lg"
          >
            系统语音
            <span v-if="activeTab === 'default'" class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></span>
          </button>
          <button 
            @click="activeTab = 'packages'" 
            :class="activeTab === 'packages' ? 'text-indigo-600' : 'text-gray-500 hover:text-gray-700'"
            class="py-3 px-6 font-medium relative transition-all text-lg"
          >
            语音包
            <span v-if="activeTab === 'packages'" class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></span>
          </button>
        </div>

        <!-- 系统语音设置 -->
        <div v-if="activeTab === 'default'" class="grid grid-cols-1 md:grid-cols-2 gap-8 animate-in fade-in-50 duration-300">
          <!-- 语言选择 -->
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors">
              语言选择
            </label>
            <div class="relative">
              <select
                v-model="selectedLanguage"
                @change="onLanguageChange"
                class="w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"
              >
                <option v-for="lang in languages" :key="lang.code" :value="lang.code">
                  {{ lang.name }}
                </option>
              </select>
              <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- 声音选择 -->
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors">
              声音选择
            </label>
            <div class="relative">
              <select
                v-model="selectedVoice"
                @change="onVoiceChange"
                class="w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"
              >
                <option v-for="voice in currentVoices" :key="voice.code" :value="voice.code">
                  {{ voice.name }}
                </option>
              </select>
              <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 语音包设置 -->
        <div v-if="activeTab === 'packages'" class="space-y-8 animate-in fade-in-50 duration-300">
          <!-- 语音包上传 -->
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors">
              上传语音包
            </label>
            <div class="flex items-center justify-center w-full">
              <label class="flex flex-col w-full h-40 border-2 border-dashed border-gray-200 rounded-xl hover:border-indigo-300 hover:bg-indigo-50/50 cursor-pointer transition-all group p-6">
                <div class="flex flex-col items-center justify-center">
                  <div class="w-14 h-14 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center text-indigo-500 group-hover:scale-110 transition-transform mb-4">
                    <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                  </div>
                  <p class="text-base text-gray-600">点击上传或拖放语音包文件</p>
                  <p class="text-xs text-gray-400 mt-1">支持 .zip, .mp3, .wav 格式</p>
                </div>
                <input type="file" class="opacity-0" @change="handleVoicePackageUpload" accept=".zip,.mp3,.wav"/>
              </label>
            </div>
          </div>

          <!-- 语音包列表 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2.5">
              已安装语音包
            </label>
            <div v-if="voicePackages.length === 0" class="text-center py-10 text-gray-500 bg-gray-50 rounded-xl">
              <div class="w-20 h-20 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center text-gray-400">
                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                </svg>
              </div>
              <p>暂无语音包，请上传</p>
            </div>
            <div class="space-y-3">
              <div 
                v-for="pkg in voicePackages" 
                :key="pkg.name" 
                class="flex items-center justify-between p-4 bg-white border border-gray-100 rounded-xl hover:border-indigo-200 hover:shadow-sm transition-all group"
              >
                <div class="flex items-center">
                  <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center text-purple-600 mr-3">
                    🎙️
                  </div>
                  <span class="font-medium group-hover:text-indigo-600 transition-colors">{{ pkg.name }}</span>
                </div>
                <div class="flex space-x-2">
                  <button 
                    @click="selectVoicePackage(pkg)"
                    :class="selectedVoicePackage === pkg.name 
                      ? 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200' 
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'"
                    class="px-3 py-1.5 rounded-lg text-sm transition-colors"
                  >
                    {{ selectedVoicePackage === pkg.name ? '已选中' : '选择' }}
                  </button>
                  <button 
                    @click="downloadVoicePackage(pkg.name)"
                    class="p-1.5 rounded-lg text-gray-500 hover:bg-gray-100 transition-colors"
                    aria-label="下载"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通用设置 -->
        <div class="pt-8 mt-8 border-t border-gray-100 grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 情感调节 -->
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors">
              情感调节
            </label>
            <div class="relative">
              <select
                v-model="selectedEmotion"
                @change="onEmotionChange"
                class="w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"
              >
                <option v-for="emotion in emotions" :key="emotion.code" :value="emotion.code">
                  {{ emotion.name }}
                </option>
              </select>
              <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
          </div>

          <!-- 语速调节 -->
          <div>
            <div class="flex justify-between items-center mb-2.5">
              <label class="block text-sm font-medium text-gray-700">
                语速
              </label>
              <span class="text-sm font-medium text-indigo-600">{{ Number(speechRate).toFixed(1) }}</span>
            </div>
            <input
              v-model.number="speechRate"
              @input="onRateChange"
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              class="w-full h-2.5 bg-gray-100 rounded-full appearance-none cursor-pointer accent-indigo-500"
            />
            <div class="flex justify-between text-xs text-gray-500 mt-1.5">
              <span>缓慢</span>
              <span>自然</span>
              <span>快速</span>
            </div>
          </div>

          <!-- 音量调节 -->
          <div>
            <div class="flex justify-between items-center mb-2.5">
              <label class="block text-sm font-medium text-gray-700">
                音量
              </label>
              <span class="text-sm font-medium text-indigo-600">{{ volume }}</span>
            </div>
            <input
              v-model.number="volume"
              @input="onVolumeChange"
              type="range"
              min="0"
              max="10"
              step="1"
              class="w-full h-2.5 bg-gray-100 rounded-full appearance-none cursor-pointer accent-indigo-500"
            />
          </div>

          <!-- TTS服务选择 -->
          <div class="group">
            <label class="block text-sm font-medium text-gray-700 mb-2.5 group-hover:text-indigo-600 transition-colors">
              语音服务
            </label>
            <div class="relative">
              <select
                v-model="localTtsService"
                @change="onTtsServiceChange"
                class="w-full px-4 py-3.5 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 transition-all appearance-none text-base"
              >
                
              </select>
              <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none text-gray-400">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部按钮区域 -->
        <div class="flex justify-end space-x-4 pt-8">
          <button
            @click="saveSettings"
            class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3.5 rounded-xl transition-all duration-300 font-medium shadow-md shadow-indigo-100/50 hover:shadow-indigo-200/50 transform hover:-translate-y-0.5 active:translate-y-0"
          >
            保存设置
          </button>
          <button
            @click="$emit('close')"
            class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3.5 rounded-xl transition-all duration-300 font-medium"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VoiceSettingsModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentSettings: {
      type: Object,
      default: () => ({
        language: 'zh-cn',
        voice: '601002',
        emotion: 'neutral',
        rate: 1.0,
        volume: 5,
        voicePackage: '',
        ttsService: 'baidu'
      })
    },
    apiBaseUrl: {
      type: String,
      default: 'http://localhost:8080'
    },
    debugMode: {
      type: Boolean,
      default: true
    },
    ttsService: {
      type: String,
      default: 'baidu',
      validator: value => ['google', 'baidu', 'custom'].includes(value)
    }
  },
  emits: ['close', 'settings-changed', 'error', 'update:ttsService'],
  data() {
    return {
      activeTab: 'default',
      selectedLanguage: 'zh-cn',
      selectedVoice: '601002',
      selectedEmotion: 'neutral',
      speechRate: 1.0,
      volume: 5,
      voicePackages: [],
      selectedVoicePackage: '',
      errorMessage: '',
      errorDetails: '',
      warningMessage: '',
      warningDetails: '',
      localTtsService: this.ttsService,
      languages: [
        { code: 'zh-cn', name: '中文(普通话)' },
        { code: 'en-us', name: '英语(美国)' },
        { code: 'ja-jp', name: '日语' },
        { code: 'ko-kr', name: '韩语' },
        { code: 'fr-fr', name: '法语' },
        { code: 'de-de', name: '德语' }
      ],
      emotions: [
        { code: 'neutral', name: '中性' },
        { code: 'happy', name: '开心' },
        { code: 'sad', name: '悲伤' },
        { code: 'angry', name: '愤怒' },
        { code: 'surprised', name: '惊讶' }
      ],
      voiceOptions: {
        'zh-cn': [
          { code: '601002', name: '智瑜 - 温暖女声' },
          { code: '601003', name: '智聆 - 通用女声' },
          { code: '601004', name: '智美 - 甜美女声' },
          { code: '601005', name: '智云 - 通用男声' },
          { code: '601006', name: '智莉 - 通用女声' },
          { code: '601007', name: '智言 - 助手女声' }
        ],
        'en-us': [
          { code: '601101', name: 'Emma - 英文女声' },
          { code: '601102', name: 'Brian - 英文男声' },
          { code: '601103', name: 'Amy - 英文女声' },
          { code: '601104', name: 'Russell - 英文男声' }
        ],
        'ja-jp': [
          { code: '601201', name: 'さくら - 日文女声' },
          { code: '601202', name: 'たかし - 日文男声' }
        ],
        'ko-kr': [
          { code: '601301', name: '지은 - 韩文女声' },
          { code: '601302', name: '민수 - 韩文男声' }
        ],
        'fr-fr': [
          { code: '601401', name: 'Céline - 法文女声' },
          { code: '601402', name: 'Pierre - 法文男声' }
        ],
        'de-de': [
          { code: '601501', name: 'Anna - 德文女声' },
          { code: '601502', name: 'Hans - 德文男声' }
        ]
      }
    };
  },
  computed: {
    currentVoices() {
      return this.voiceOptions[this.selectedLanguage] || [];
    },
    voicePackagesListApi() {
      return `${this.apiBaseUrl}/api/voice/packages/list`;
    },
    voicePackagesUploadApi() {
      return `${this.apiBaseUrl}/api/voice/packages/upload`;
    },
    voiceSettingsApi() {
      return `${this.apiBaseUrl}/api/voice/settings`;
    }
  },
  methods: {
    // 显示错误信息
    showError(message, details = '') {
      this.errorMessage = message;
      this.errorDetails = details;
      this.warningMessage = '';
      this.warningDetails = '';
      this.$emit('error', { message, details });
      
      setTimeout(() => {
        this.errorMessage = '';
        this.errorDetails = '';
      }, 8000);
      
      if (this.debugMode) {
        console.error('语音设置错误:', message, '\n详情:', details);
      }
    },
    
    // 显示警告信息（非阻塞）
    showWarning(message, details = '') {
      this.warningMessage = message;
      this.warningDetails = details;
      this.errorMessage = '';
      this.errorDetails = '';
      
      setTimeout(() => {
        this.warningMessage = '';
        this.warningDetails = '';
      }, 5000);
      
      if (this.debugMode) {
        console.warn('语音设置警告:', message, '\n详情:', details);
      }
    },
    
    // 验证URL格式
    isValidUrl(url) {
      try {
        new URL(url);
        return true;
      } catch (e) {
        return false;
      }
    },
    
    // 语言改变时的处理
    onLanguageChange() {
      const voices = this.currentVoices;
      if (voices.length > 0) {
        this.selectedVoice = voices[0].code;
      }
    },
    
    // 语音改变时的处理
    onVoiceChange() {
      if (this.debugMode) {
        console.log('选择了新语音:', this.selectedVoice);
      }
    },
    
    // 情感改变时的处理
    onEmotionChange() {
      if (this.debugMode) {
        console.log('选择了新情感:', this.selectedEmotion);
      }
    },
    
    // 语速改变时的处理
    onRateChange() {
      if (this.debugMode) {
        console.log('语速调整为:', this.speechRate);
      }
    },
    
    // 音量改变时的处理
    onVolumeChange() {
      if (this.debugMode) {
        console.log('音量调整为:', this.volume);
      }
    },
    
    // TTS服务改变时的处理
    onTtsServiceChange() {
      if (this.debugMode) {
        console.log('切换到TTS服务:', this.localTtsService);
      }
      this.$emit('update:ttsService', this.localTtsService);
    },
    
    // 保存设置
    async saveSettings() {
      const settings = {
        language: this.selectedLanguage,
        emotion: this.selectedEmotion,
        rate: Number(this.speechRate),
        volume: Number(this.volume),
        voicePackage: this.activeTab === 'packages' ? this.selectedVoicePackage : '',
        ttsService: this.localTtsService
      };
      
      if (this.activeTab === 'default') {
        settings.voice = this.selectedVoice;
      }
      
      try {
        const response = await fetch(this.voiceSettingsApi, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settings)
        });
        
        if (!response.ok) {
          const responseText = await response.text();
          throw new Error(`保存设置失败，状态码: ${response.status}, 内容: ${responseText}`);
        }
        
        localStorage.setItem('voiceSettings', JSON.stringify(settings));
        
        this.$emit('settings-changed', settings);
        this.$emit('update:ttsService', this.localTtsService);
        this.$emit('close');
      } catch (error) {
        console.error('保存设置失败:', error);
        this.showError('保存设置失败: ' + (error.message || '未知错误'));
      }
    },
    
    // 加载设置
    async loadSettings() {
      try {
        const response = await fetch(this.voiceSettingsApi);
        if (response.ok) {
          const result = await response.json();
          if (result.code === 200 && result.data) {
            const settings = result.data;
            this.selectedLanguage = settings.language || 'zh-cn';
            this.selectedVoice = settings.voice || '601002';
            this.selectedEmotion = settings.emotion || 'neutral';
            this.speechRate = Number(settings.rate) || 1.0;
            this.volume = Number(settings.volume) || 5;
            this.selectedVoicePackage = settings.voicePackage || '';
            this.localTtsService = settings.ttsService || 'baidu';
            
            if (this.selectedVoicePackage) {
              this.activeTab = 'packages';
            }
            return;
          }
        }
      } catch (error) {
        console.warn('从后端获取设置失败，将使用本地存储:', error);
      }
      
      const saved = localStorage.getItem('voiceSettings');
      if (saved) {
        try {
          const settings = JSON.parse(saved);
          this.selectedLanguage = settings.language || 'zh-cn';
          this.selectedVoice = settings.voice || '601002';
          this.selectedEmotion = settings.emotion || 'neutral';
          this.speechRate = Number(settings.rate) || 1.0;
          this.volume = Number(settings.volume) || 5;
          this.selectedVoicePackage = settings.voicePackage || '';
          this.localTtsService = settings.ttsService || 'baidu';
          
          if (this.selectedVoicePackage) {
            this.activeTab = 'packages';
          }
        } catch (error) {
          console.error('加载本地设置失败:', error);
          this.showError('加载保存的设置失败');
        }
      }
    },
    
    // 获取语音包列表
    async fetchVoicePackages() {
      try {
        const response = await fetch(this.voicePackagesListApi);
        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            this.voicePackages = result.data || [];
          } else {
            this.showError('获取语音包列表失败: ' + (result.msg || '未知错误'));
          }
        } else {
          const responseText = await response.text();
          this.showError('获取语音包列表请求失败', `状态码: ${response.status}, 内容: ${responseText}`);
        }
      } catch (error) {
        console.error('获取语音包列表失败:', error);
        this.showError('无法连接到服务器获取语音包列表', error.message);
      }
    },
    
    // 处理语音包上传
    async handleVoicePackageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;
      
      if (file.size > 100 * 1024 * 1024) {
        this.showError('文件过大', '请上传小于100MB的文件');
        return;
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      try {
        const response = await fetch(this.voicePackagesUploadApi, {
          method: 'POST',
          body: formData
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.code === 200) {
            this.showError('语音包上传成功', result.msg || '');
            this.fetchVoicePackages();
            event.target.value = '';
          } else {
            this.showError('上传失败: ' + (result.msg || '未知错误'));
          }
        } else {
          const responseText = await response.text();
          this.showError('上传请求失败', `状态码: ${response.status}, 内容: ${responseText}`);
        }
      } catch (error) {
        console.error('上传语音包失败:', error);
        this.showError('网络错误，上传失败', error.message);
      }
    },
    
    // 选择语音包
    selectVoicePackage(pkg) {
      this.selectedVoicePackage = pkg.name;
    },
    
    // 下载语音包
    downloadVoicePackage(filename) {
      try {
        const url = `${this.apiBaseUrl}/api/voice/packages/download?filename=${encodeURIComponent(filename)}`;
        window.open(url, '_blank');
      } catch (error) {
        console.error('下载语音包失败:', error);
        this.showError('下载失败: ' + (error.message || '未知错误'));
      }
    }
  },
  
  watch: {
    show(newVal) {
      if (newVal) {
        this.fetchVoicePackages();
        this.loadSettings();
      }
    },
    
    ttsService(newVal) {
      this.localTtsService = newVal;
    },
    
    currentSettings: {
      handler(newSettings) {
        if (newSettings) {
          this.selectedLanguage = newSettings.language || 'zh-cn';
          this.selectedVoice = newSettings.voice || '601002';
          this.selectedEmotion = newSettings.emotion || 'neutral';
          this.speechRate = Number(newSettings.rate) || 1.0;
          this.volume = Number(newSettings.volume) || 5;
          this.selectedVoicePackage = newSettings.voicePackage || '';
          this.localTtsService = newSettings.ttsService || 'baidu';
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style scoped>
/* 自定义动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInFromTop {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-in {
  animation-fill-mode: both;
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.fade-in-50 {
  animation: fadeIn 0.3s ease-out 0.1s forwards;
  opacity: 0;
}

.slide-in-from-top {
  animation: slideInFromTop 0.3s ease-out forwards;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.4);
}

/* 自定义滑块样式 */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 22px;
  width: 22px;
  border-radius: 50%;
  background: currentColor;
  cursor: pointer;
  transition: all 0.15s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: 0 0 0 6px rgba(99, 102, 241, 0.1);
}
</style>
