/**
 * 阿里云百炼服务封装类
 * 提供文档分析、智能分段、关键词提取等AI能力
 */

class BailianService {
  constructor(config = {}) {
    this.accessKeyId = config.accessKeyId || 'LTAI5tB38aLEpHToKESq9W3T'
    this.accessKeySecret = config.accessKeySecret || '******************************'
    this.workspaceId = config.workspaceId || 'llm-xz4u83kacijf8jaj'
    this.apiKey = config.apiKey || 'sk-0baf684faf044823a593986c15617b12'
    this.baseUrl = 'https://dashscope.aliyuncs.com/api/v1'
    this.enabled = true // 可以通过配置控制是否启用百炼服务
  }

  /**
   * 检查服务是否可用
   */
  isEnabled() {
    return this.enabled && this.apiKey && this.workspaceId
  }

  /**
   * 通用API调用方法 - 现在通过后端代理
   */
  async callAPI(endpoint, data = {}) {
    if (!this.isEnabled()) {
      throw new Error('百炼服务未启用或配置不完整')
    }

    try {
      // 通过后端API调用，解决CORS问题
      const backendUrl = 'http://localhost:8081/api/knowledge-base'
      let backendEndpoint = ''

      // 映射百炼API端点到后端端点
      if (endpoint.includes('text-generation')) {
        if (data.input && data.input.prompt) {
          const prompt = data.input.prompt
          if (prompt.includes('分段') || prompt.includes('段落')) {
            backendEndpoint = '/segment-document'
            data = {
              content: this.extractContentFromPrompt(prompt),
              maxLength: 1000,
              strategy: 'auto'
            }
          } else if (prompt.includes('关键词')) {
            backendEndpoint = '/extract-keywords'
            data = {
              content: this.extractContentFromPrompt(prompt)
            }
          } else if (prompt.includes('摘要')) {
            backendEndpoint = '/generate-summary'
            data = {
              content: this.extractContentFromPrompt(prompt)
            }
          } else if (prompt.includes('分析')) {
            backendEndpoint = '/analyze-document'
            data = {
              content: this.extractContentFromPrompt(prompt)
            }
          }
        }
      }

      if (!backendEndpoint) {
        throw new Error('不支持的API调用类型')
      }

      console.log(`通过后端调用API: ${backendEndpoint}`, data)

      const response = await fetch(`${backendUrl}${backendEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`后端API调用失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('后端API调用成功:', result)

      // 转换后端响应格式为百炼格式
      return this.convertBackendResponse(result, backendEndpoint)

    } catch (error) {
      console.error('API调用错误:', error)
      throw error
    }
  }

  /**
   * 从提示词中提取内容
   */
  extractContentFromPrompt(prompt) {
    // 提取提示词中的实际内容
    const lines = prompt.split('\n')
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line && !line.includes('请') && !line.includes('：') && line.length > 20) {
        return lines.slice(i).join('\n')
      }
    }
    return prompt
  }

  /**
   * 转换后端响应为百炼格式
   */
  convertBackendResponse(result, endpoint) {
    switch (endpoint) {
      case '/segment-document':
        return {
          output: {
            text: JSON.stringify({ segments: result.segments })
          }
        }
      case '/extract-keywords':
        return {
          output: {
            text: result.keywords.join(',')
          }
        }
      case '/generate-summary':
        return {
          output: {
            text: result.summary
          }
        }
      case '/analyze-document':
        return {
          output: {
            text: JSON.stringify(result)
          }
        }
      default:
        return result
    }
  }

  /**
   * 智能文档分段
   * @param {string} content - 文档内容
   * @param {Object} options - 分段选项
   * @returns {Promise<Array>} 分段结果
   */
  async segmentDocument(content, options = {}) {
    const { maxLength = 1000 } = options

    const prompt = `请对以下文档进行智能分段。要求：
1. 按照语义边界进行分段，保持内容的完整性
2. 每个分段长度控制在${maxLength}字符以内
3. 为每个分段提取3-5个关键词
4. 为每个分段生成简洁的摘要（50字以内）
5. 返回JSON格式，包含segments数组，每个元素包含：id, content, keywords, summary

文档内容：
${content}

请返回标准的JSON格式结果。`

    try {
      const response = await this.callAPI('/services/aigc/text-generation/generation', {
        model: 'qwen-turbo',
        input: {
          prompt: prompt
        },
        parameters: {
          max_tokens: 4000,
          temperature: 0.1
        }
      })

      if (response.output && response.output.text) {
        try {
          // 尝试解析JSON响应
          const jsonMatch = response.output.text.match(/\{[\s\S]*\}/)
          if (jsonMatch) {
            const result = JSON.parse(jsonMatch[0])
            return this.formatSegments(result.segments || [])
          }
        } catch (parseError) {
          console.warn('解析百炼响应失败，使用备用解析方法')
          return this.parseTextResponse(response.output.text, content)
        }
      }

      throw new Error('百炼服务返回格式异常')
    } catch (error) {
      console.error('百炼分段失败:', error)
      throw error
    }
  }

  /**
   * 解析文本响应（备用方法）
   */
  parseTextResponse(text, originalContent) {
    const segments = []
    const lines = text.split('\n').filter(line => line.trim())
    
    let currentSegment = null
    let segmentId = 1

    for (const line of lines) {
      if (line.includes('分段') || line.includes('段落')) {
        if (currentSegment) {
          segments.push(currentSegment)
        }
        currentSegment = {
          id: segmentId++,
          content: '',
          keywords: [],
          summary: ''
        }
      } else if (line.includes('关键词') || line.includes('关键字')) {
        if (currentSegment) {
          const keywords = line.replace(/.*[关键词字][:：]?\s*/, '').split(/[,，、\s]+/).filter(k => k.trim())
          currentSegment.keywords = keywords.slice(0, 5)
        }
      } else if (line.includes('摘要') || line.includes('总结')) {
        if (currentSegment) {
          currentSegment.summary = line.replace(/.*[摘要总结][:：]?\s*/, '').trim()
        }
      } else if (currentSegment && line.trim() && !line.includes('JSON') && !line.includes('{') && !line.includes('}')) {
        currentSegment.content += (currentSegment.content ? '\n' : '') + line.trim()
      }
    }

    if (currentSegment) {
      segments.push(currentSegment)
    }

    return segments.length > 0 ? segments : this.fallbackSegmentation(originalContent)
  }

  /**
   * 备用分段方法
   */
  fallbackSegmentation(content) {
    const segments = []
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
    
    paragraphs.forEach((paragraph, index) => {
      segments.push({
        id: index + 1,
        content: paragraph.trim(),
        keywords: this.extractSimpleKeywords(paragraph),
        summary: this.generateSimpleSummary(paragraph)
      })
    })

    return segments
  }

  /**
   * 简单关键词提取
   */
  extractSimpleKeywords(text) {
    const words = text.match(/[\u4e00-\u9fa5]{2,}|[a-zA-Z]{3,}/g) || []
    const wordCount = {}
    
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1
    })

    return Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word)
  }

  /**
   * 简单摘要生成
   */
  generateSimpleSummary(text) {
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim())
    return sentences[0] ? sentences[0].trim().substring(0, 50) + '...' : text.substring(0, 50) + '...'
  }

  /**
   * 格式化分段结果
   */
  formatSegments(segments) {
    return segments.map((segment, index) => ({
      id: segment.id || index + 1,
      content: segment.content || '',
      keywords: Array.isArray(segment.keywords) ? segment.keywords : [],
      summary: segment.summary || '',
      length: (segment.content || '').length,
      createdAt: new Date(),
      source: 'bailian'
    }))
  }

  /**
   * 提取关键词
   * @param {string} content - 文本内容
   * @returns {Promise<Array>} 关键词列表
   */
  async extractKeywords(content) {
    const prompt = `请从以下文本中提取5-8个最重要的关键词，用逗号分隔：\n${content.substring(0, 1000)}`

    try {
      const response = await this.callAPI('/services/aigc/text-generation/generation', {
        model: 'qwen-turbo',
        input: { prompt },
        parameters: {
          max_tokens: 100,
          temperature: 0.1
        }
      })

      if (response.output && response.output.text) {
        return response.output.text.split(/[,，、\s]+/).filter(k => k.trim()).slice(0, 8)
      }

      return []
    } catch (error) {
      console.error('关键词提取失败:', error)
      return this.extractSimpleKeywords(content)
    }
  }

  /**
   * 生成摘要
   * @param {string} content - 文本内容
   * @returns {Promise<string>} 摘要
   */
  async generateSummary(content) {
    const prompt = `请为以下文本生成一个简洁的摘要（50字以内）：\n${content.substring(0, 1000)}`

    try {
      const response = await this.callAPI('/services/aigc/text-generation/generation', {
        model: 'qwen-turbo',
        input: { prompt },
        parameters: {
          max_tokens: 100,
          temperature: 0.1
        }
      })

      if (response.output && response.output.text) {
        return response.output.text.trim().substring(0, 200)
      }

      return this.generateSimpleSummary(content)
    } catch (error) {
      console.error('摘要生成失败:', error)
      return this.generateSimpleSummary(content)
    }
  }

  /**
   * 文档质量分析
   * @param {string} content - 文档内容
   * @returns {Promise<Object>} 分析结果
   */
  async analyzeDocument(content) {
    const prompt = `请分析以下文档的结构和质量，包括：
1. 文档类型（技术文档、学术论文、新闻报道等）
2. 结构完整性（1-10分）
3. 内容质量（1-10分）
4. 建议的分段策略
5. 主要主题

文档内容：
${content.substring(0, 2000)}

请返回JSON格式结果。`

    try {
      const response = await this.callAPI('/services/aigc/text-generation/generation', {
        model: 'qwen-turbo',
        input: { prompt },
        parameters: {
          max_tokens: 500,
          temperature: 0.1
        }
      })

      if (response.output && response.output.text) {
        try {
          const jsonMatch = response.output.text.match(/\{[\s\S]*\}/)
          if (jsonMatch) {
            return JSON.parse(jsonMatch[0])
          }
        } catch (parseError) {
          // 解析失败时返回默认分析
        }
      }

      return {
        documentType: '通用文档',
        structureScore: 7,
        qualityScore: 7,
        recommendedStrategy: 'auto',
        mainTopics: ['通用内容']
      }
    } catch (error) {
      console.error('文档分析失败:', error)
      return {
        documentType: '未知',
        structureScore: 5,
        qualityScore: 5,
        recommendedStrategy: 'auto',
        mainTopics: []
      }
    }
  }

  /**
   * 批量处理文档
   * @param {Array} documents - 文档列表
   * @param {Function} progressCallback - 进度回调
   * @returns {Promise<Array>} 处理结果
   */
  async batchProcess(documents, progressCallback) {
    const results = []
    const total = documents.length

    for (let i = 0; i < documents.length; i++) {
      const doc = documents[i]
      
      try {
        if (progressCallback) {
          progressCallback({
            current: i + 1,
            total,
            status: 'processing',
            document: doc.name
          })
        }

        const segments = await this.segmentDocument(doc.content)
        results.push({
          ...doc,
          segments,
          status: 'completed',
          processedAt: new Date()
        })

        // 添加延迟避免API限流
        if (i < documents.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

      } catch (error) {
        console.error(`处理文档 ${doc.name} 失败:`, error)
        results.push({
          ...doc,
          segments: [],
          status: 'error',
          error: error.message,
          processedAt: new Date()
        })
      }
    }

    if (progressCallback) {
      progressCallback({
        current: total,
        total,
        status: 'completed'
      })
    }

    return results
  }
}

export default BailianService
