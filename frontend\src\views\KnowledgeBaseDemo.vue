<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">知识库管理系统</h1>
        <p class="text-gray-600">完整的知识库创建、管理和编辑流程演示</p>
      </div>

      <!-- 功能按钮 -->
      <div class="flex justify-center space-x-4 mb-8">
        <button 
          @click="showCreateKnowledgeBase"
          class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-semibold"
        >
          创建新知识库
        </button>
        <button 
          @click="showExistingKnowledgeBases"
          class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-semibold"
        >
          管理现有知识库
        </button>
      </div>

      <!-- 知识库列表 -->
      <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-900">我的知识库</h2>
          <span class="text-sm text-gray-600">共 {{ knowledgeBases.length }} 个知识库</span>
        </div>

        <div v-if="knowledgeBases.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div 
            v-for="kb in knowledgeBases" 
            :key="kb.id"
            class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all duration-200"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div :class="[
                  'w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl',
                  kb.type === 'text' ? 'bg-blue-500' : kb.type === 'table' ? 'bg-green-500' : 'bg-orange-500'
                ]">
                  {{ kb.type === 'text' ? '📄' : kb.type === 'table' ? '📊' : '🖼️' }}
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">{{ kb.name }}</h3>
                  <p class="text-sm text-gray-600">{{ getTypeLabel(kb.type) }}</p>
                </div>
              </div>
              <div class="flex space-x-2">
                <button 
                  @click="editKnowledgeBase(kb)"
                  class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="编辑内容"
                >
                  ✏️
                </button>
                <button 
                  @click="deleteKnowledgeBase(kb.id)"
                  class="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            <p class="text-sm text-gray-600 mb-4">{{ kb.description }}</p>
            
            <div class="flex items-center justify-between text-sm text-gray-500">
              <span>{{ kb.itemCount }} 项内容</span>
              <span>{{ formatDate(kb.updateTime) }}</span>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-100">
              <div class="flex space-x-2">
                <button 
                  @click="viewKnowledgeBase(kb)"
                  class="flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                >
                  查看详情
                </button>
                <button 
                  @click="editKnowledgeBase(kb)"
                  class="flex-1 px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
                >
                  编辑内容
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-12">
          <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <span class="text-4xl text-gray-400">📚</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">还没有知识库</h3>
          <p class="text-gray-600 mb-6">创建您的第一个知识库，开始管理文档和数据</p>
          <button 
            @click="showCreateKnowledgeBase"
            class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            创建知识库
          </button>
        </div>
      </div>

      <!-- 流程说明 -->
      <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">知识库管理流程</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">📁</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">1. 上传文件</h3>
            <p class="text-sm text-gray-600">支持 PDF、DOC、DOCX、MD、TXT 等多种格式</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-green-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">⚙️</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">2. 创建设置</h3>
            <p class="text-sm text-gray-600">配置解析模式、分段策略和索引设置</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-yellow-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">👁️</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">3. 分段预览</h3>
            <p class="text-sm text-gray-600">预览文档分段结果，支持手动调整</p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-3 bg-purple-100 rounded-full flex items-center justify-center">
              <span class="text-2xl">🚀</span>
            </div>
            <h3 class="font-semibold text-gray-900 mb-2">4. 数据处理</h3>
            <p class="text-sm text-gray-600">自动建立索引，完成知识库创建</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识库管理器 -->
    <KnowledgeBaseManager
      :show="showManager"
      :knowledgeBase="currentKnowledgeBase"
      @close="showManager = false"
      @complete="onKnowledgeBaseComplete"
    />

    <!-- 知识库编辑器 -->
    <KnowledgeBaseEditor
      :show="showEditor"
      :knowledgeBase="currentKnowledgeBase"
      @close="showEditor = false"
      @save="onKnowledgeBaseSave"
    />
  </div>
</template>

<script>
import KnowledgeBaseManager from '../components/KnowledgeBaseManager.vue'
import KnowledgeBaseEditor from '../components/KnowledgeBaseEditor.vue'

export default {
  name: 'KnowledgeBaseDemo',
  components: {
    KnowledgeBaseManager,
    KnowledgeBaseEditor
  },
  
  data() {
    return {
      showManager: false,
      showEditor: false,
      currentKnowledgeBase: null,
      
      // 模拟知识库数据
      knowledgeBases: [
        {
          id: 1,
          name: 'Go语言文档',
          description: 'Go语言学习资料和API文档',
          type: 'text',
          itemCount: 156,
          updateTime: new Date('2024-01-15'),
          status: 'completed'
        },
        {
          id: 2,
          name: '产品需求表格',
          description: '产品功能需求和用户反馈数据',
          type: 'table',
          itemCount: 89,
          updateTime: new Date('2024-01-10'),
          status: 'completed'
        },
        {
          id: 3,
          name: '设计素材库',
          description: 'UI设计稿和产品截图',
          type: 'image',
          itemCount: 234,
          updateTime: new Date('2024-01-08'),
          status: 'completed'
        }
      ]
    }
  },

  methods: {
    showCreateKnowledgeBase() {
      this.currentKnowledgeBase = {
        name: '',
        description: '',
        type: 'text'
      }
      this.showManager = true
    },

    showExistingKnowledgeBases() {
      // 这里可以添加显示现有知识库列表的逻辑
      alert('显示现有知识库管理界面')
    },

    editKnowledgeBase(kb) {
      this.currentKnowledgeBase = kb
      this.showEditor = true
    },

    viewKnowledgeBase(kb) {
      alert(`查看知识库: ${kb.name}\n类型: ${this.getTypeLabel(kb.type)}\n内容数量: ${kb.itemCount}`)
    },

    deleteKnowledgeBase(id) {
      if (confirm('确定要删除这个知识库吗？此操作不可恢复。')) {
        this.knowledgeBases = this.knowledgeBases.filter(kb => kb.id !== id)
      }
    },

    onKnowledgeBaseComplete(result) {
      // 添加新创建的知识库
      this.knowledgeBases.push({
        ...result,
        id: Date.now(),
        itemCount: result.files?.length || 0,
        updateTime: new Date(),
        status: 'completed'
      })
      this.showManager = false
      alert('知识库创建成功！')
    },

    onKnowledgeBaseSave(result) {
      // 更新知识库信息
      const index = this.knowledgeBases.findIndex(kb => kb.id === result.knowledgeBase.id)
      if (index !== -1) {
        this.knowledgeBases[index] = {
          ...this.knowledgeBases[index],
          ...result.knowledgeBase,
          updateTime: new Date()
        }
      }
      this.showEditor = false
      alert('知识库保存成功！')
    },

    getTypeLabel(type) {
      const labels = {
        text: '文本格式',
        table: '表格格式', 
        image: '图片格式'
      }
      return labels[type] || '未知格式'
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
