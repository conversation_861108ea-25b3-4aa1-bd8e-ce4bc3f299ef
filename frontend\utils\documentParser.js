/**
 * 文档解析工具类
 * 支持文本、Markdown和Word文档的真实解析和分段处理
 */

import mammoth from 'mammoth'

class DocumentParser {
  constructor() {
    this.supportedTypes = {
      'text/plain': 'txt',
      'text/markdown': 'md',
      'application/msword': 'doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
    }
  }

  /**
   * 解析文档内容
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 解析后的文本内容
   */
  async parseDocument(file) {
    const fileType = this.getFileType(file)
    
    switch (fileType) {
      case 'txt':
        return await this.parseTextFile(file)
      case 'md':
        return await this.parseMarkdownFile(file)
      case 'doc':
      case 'docx':
        return await this.parseWordFile(file)
      default:
        throw new Error(`不支持的文件类型: ${file.type}。支持的格式：TXT、Markdown、Word文档(.docx)`)
    }
  }

  /**
   * 获取文件类型
   * @param {File} file - 文件对象
   * @returns {string} 文件类型
   */
  getFileType(file) {
    // 首先根据 MIME 类型判断
    if (this.supportedTypes[file.type]) {
      return this.supportedTypes[file.type]
    }
    
    // 如果 MIME 类型不准确，根据文件扩展名判断
    const extension = file.name.split('.').pop().toLowerCase()
    const extensionMap = {
      'txt': 'txt',
      'md': 'md',
      'markdown': 'md',
      'doc': 'doc',
      'docx': 'docx'
    }
    
    return extensionMap[extension] || 'unknown'
  }

  /**
   * 文件转ArrayBuffer
   * @param {File} file - 文件对象
   * @returns {Promise<ArrayBuffer>} ArrayBuffer
   */
  async fileToArrayBuffer(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 解析文本文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文本内容
   */
  async parseTextFile(file) {
    try {
      // 首先尝试UTF-8编码
      let content = await this.readTextWithEncoding(file, 'UTF-8')

      // 检查是否包含乱码字符，如果是则尝试其他编码
      if (this.containsGarbledText(content)) {
        console.warn('检测到可能的编码问题，尝试GBK编码')
        try {
          content = await this.readTextWithEncoding(file, 'GBK')
        } catch (gbkError) {
          console.warn('GBK编码读取失败，使用UTF-8结果')
        }
      }

      // 清理和标准化文本
      content = this.cleanTextContent(content)

      if (!content || content.length < 10) {
        throw new Error('文本文件内容过短或为空')
      }

      return content

    } catch (error) {
      throw new Error(`文本文件解析失败: ${error.message}`)
    }
  }

  /**
   * 使用指定编码读取文本
   * @param {File} file - 文件对象
   * @param {string} encoding - 编码格式
   * @returns {Promise<string>} 文本内容
   */
  async readTextWithEncoding(file, encoding) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = () => reject(new Error(`使用${encoding}编码读取失败`))
      reader.readAsText(file, encoding)
    })
  }

  /**
   * 检查文本是否包含乱码
   * @param {string} text - 文本内容
   * @returns {boolean} 是否包含乱码
   */
  containsGarbledText(text) {
    // 检查是否包含常见的乱码字符
    const garbledPatterns = [
      /[��]/g,  // 替换字符
      /[\uFFFD]/g,  // Unicode替换字符
      /[^\u0020-\u007F\u4e00-\u9fa5\u3000-\u303F\uFF00-\uFFEF]/g  // 非ASCII、非中文、非标点的字符
    ]

    return garbledPatterns.some(pattern => pattern.test(text))
  }

  /**
   * 清理文本内容
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanTextContent(text) {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{4,}/g, '\n\n\n')
      .replace(/[ \t]+/g, ' ')
      .replace(/^\s+|\s+$/gm, '')
      .trim()
  }

  /**
   * 解析Markdown文件
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文本内容
   */
  async parseMarkdownFile(file) {
    try {
      let content = await this.parseTextFile(file)

      // 保留Markdown的结构信息，但清理一些格式标记
      content = this.cleanMarkdownContent(content)

      return content

    } catch (error) {
      throw new Error(`Markdown文件解析失败: ${error.message}`)
    }
  }

  /**
   * 清理Markdown内容
   * @param {string} text - 原始Markdown文本
   * @returns {string} 清理后的文本
   */
  cleanMarkdownContent(text) {
    return text
      // 保留标题结构
      .replace(/^#{1,6}\s+/gm, (match) => match)
      // 清理链接但保留文本
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // 清理图片标记
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[图片: $1]')
      // 清理粗体和斜体标记
      .replace(/\*\*([^*]+)\*\*/g, '$1')
      .replace(/\*([^*]+)\*/g, '$1')
      .replace(/__([^_]+)__/g, '$1')
      .replace(/_([^_]+)_/g, '$1')
      // 清理代码块标记但保留内容
      .replace(/```[\s\S]*?```/g, (match) => {
        return match.replace(/```\w*\n?/g, '').replace(/```/g, '')
      })
      // 清理行内代码标记
      .replace(/`([^`]+)`/g, '$1')
      // 清理引用标记
      .replace(/^>\s*/gm, '')
      // 清理列表标记但保留结构
      .replace(/^[\s]*[-*+]\s+/gm, '• ')
      .replace(/^[\s]*\d+\.\s+/gm, (_, offset, string) => {
        const lineStart = string.lastIndexOf('\n', offset) + 1
        const indent = offset - lineStart
        return ' '.repeat(indent) + '1. '
      })
      // 标准化换行
      .replace(/\n{3,}/g, '\n\n')
      .trim()
  }





  /**
   * 解析Word文件（使用 mammoth.js 真实解析）
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文本内容
   */
  async parseWordFile(file) {
    try {
      const arrayBuffer = await this.fileToArrayBuffer(file)
      const fileType = this.getFileType(file)

      console.log(`开始解析Word文档: ${file.name}, 类型: ${fileType}, 大小: ${file.size} bytes`)

      // 检查是否为旧版.doc格式
      if (fileType === 'doc') {
        throw new Error('不支持旧版.doc格式文档。请将文档转换为.docx格式后重试。')
      }

      // 解析.docx格式
      const result = await mammoth.extractRawText({ arrayBuffer })

      console.log('mammoth解析结果:', {
        hasValue: !!result.value,
        valueLength: result.value ? result.value.length : 0,
        messagesCount: result.messages ? result.messages.length : 0
      })

      if (!result.value || !result.value.trim()) {
        // 尝试使用HTML提取方式
        console.log('尝试使用HTML提取方式...')
        const htmlResult = await mammoth.convertToHtml({ arrayBuffer })

        if (htmlResult.value) {
          // 从HTML中提取纯文本
          const textContent = this.extractTextFromHTML(htmlResult.value)
          if (textContent && textContent.trim()) {
            console.log('HTML提取成功，文本长度:', textContent.length)
            return this.cleanWordText(textContent)
          }
        }

        throw new Error('Word文档中未找到可提取的文本内容。请检查文档是否包含文本或尝试重新保存文档。')
      }

      // 如果有警告信息，记录但不影响解析
      if (result.messages && result.messages.length > 0) {
        console.warn('Word文档解析警告:', result.messages.map(m => m.message))
      }

      const cleanedText = this.cleanWordText(result.value)
      console.log('解析完成，最终文本长度:', cleanedText.length)

      return cleanedText

    } catch (error) {
      console.error('Word文档解析失败:', error)
      throw new Error(`Word文档解析失败: ${error.message}`)
    }
  }

  /**
   * 从HTML中提取纯文本
   * @param {string} html - HTML内容
   * @returns {string} 纯文本内容
   */
  extractTextFromHTML(html) {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // 移除script和style标签
    const scripts = tempDiv.querySelectorAll('script, style')
    scripts.forEach(el => el.remove())

    // 获取纯文本内容
    let text = tempDiv.textContent || tempDiv.innerText || ''

    // 清理文本
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim()
  }

  /**
   * 清理Word文档文本
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanWordText(text) {
    if (!text) return ''

    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\t+/g, ' ')
      .replace(/[ ]{2,}/g, ' ')
      .replace(/^\s+|\s+$/gm, '')
      .trim()
  }



  /**
   * 文档分段处理
   * @param {string} content - 文档内容
   * @param {Object} settings - 分段设置
   * @returns {Array} 分段结果
   */
  segmentDocument(content, settings = {}) {
    const {
      strategy = 'auto',
      maxLength = 1000,
      overlap = 100,
      separator = '\n\n'
    } = settings

    switch (strategy) {
      case 'auto':
        return this.autoSegment(content, maxLength, overlap)
      case 'fixed':
        return this.fixedLengthSegment(content, maxLength, overlap)
      case 'manual':
        return this.manualSegment(content, separator)
      default:
        return this.autoSegment(content, maxLength, overlap)
    }
  }

  /**
   * 自动分段（基于段落和语义）
   * @param {string} content - 文档内容
   * @param {number} maxLength - 最大长度
   * @param {number} overlap - 重叠长度
   * @returns {Array} 分段结果
   */
  autoSegment(content, maxLength = 1000, overlap = 100) {
    console.log('开始自动分段，内容长度:', content.length, '最大分段长度:', maxLength, '重叠长度:', overlap)

    if (!content || content.trim().length === 0) {
      console.warn('内容为空，无法分段')
      return []
    }

    const segments = []

    try {
      // 首先按章节分割（基于标题模式）
      const sections = this.splitByHeaders(content)
      console.log('按标题分割结果:', sections.length, '个章节')

      let segmentId = 1

      for (let i = 0; i < sections.length; i++) {
        const section = sections[i]
        console.log(`处理第${i + 1}个章节，长度:`, section.length)

        if (section.length <= maxLength) {
          // 如果章节长度合适，直接作为一个分段
          const segment = this.createSegment(segmentId++, section.trim())
          segments.push(segment)
          console.log(`创建分段 ${segment.id}，长度: ${segment.length}`)
        } else {
          // 如果章节太长，进一步分段
          console.log(`章节过长(${section.length}字符)，需要进一步分段`)
          const subSegments = this.splitLongSection(section, maxLength, overlap)
          console.log(`分割为 ${subSegments.length} 个子分段`)

          subSegments.forEach((subSegment) => {
            const segment = this.createSegment(segmentId++, subSegment.trim())
            segments.push(segment)
            console.log(`创建子分段 ${segment.id}，长度: ${segment.length}`)
          })
        }
      }

      console.log('自动分段完成，共生成', segments.length, '个分段')
      return segments

    } catch (error) {
      console.error('自动分段失败:', error)
      // 回退到简单的固定长度分段
      console.log('回退到固定长度分段')
      return this.fixedLengthSegment(content, maxLength, overlap)
    }
  }

  /**
   * 按标题分割内容
   * @param {string} content - 文档内容
   * @returns {Array} 章节数组
   */
  splitByHeaders(content) {
    console.log('开始按标题分割，内容长度:', content.length)

    // 识别标题模式
    const headerPatterns = [
      /^第[一二三四五六七八九十\d]+[章节部分]/m,
      /^\d+\.\s+/m,
      /^[一二三四五六七八九十]+[、.]/m,
      /^#{1,6}\s+/m, // Markdown标题
      /^.{1,50}：\s*$/m // 冒号结尾的标题
    ]

    const lines = content.split('\n')
    const sections = []
    let currentSection = ''

    for (const line of lines) {
      const isHeader = headerPatterns.some(pattern => pattern.test(line.trim()))

      if (isHeader && currentSection.trim()) {
        // 遇到新标题，保存当前章节
        sections.push(currentSection.trim())
        currentSection = line
      } else {
        currentSection += (currentSection ? '\n' : '') + line
      }
    }

    // 添加最后一个章节
    if (currentSection.trim()) {
      sections.push(currentSection.trim())
    }

    console.log('标题分割结果:', sections.length, '个章节')

    // 如果没有识别到标题或只有一个大章节，按段落分割
    if (sections.length <= 1) {
      console.log('未识别到标题，按段落分割')
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
      console.log('段落分割结果:', paragraphs.length, '个段落')

      // 如果段落也很少，按句子分割
      if (paragraphs.length <= 2) {
        console.log('段落太少，按句子分割')
        const sentences = content.split(/[。！？.!?]\s*/).filter(s => s.trim().length > 20)
        console.log('句子分割结果:', sentences.length, '个句子')
        return sentences.length > 1 ? sentences : [content]
      }

      return paragraphs
    }

    return sections
  }

  /**
   * 分割长章节
   * @param {string} section - 章节内容
   * @param {number} maxLength - 最大长度
   * @param {number} overlap - 重叠长度
   * @returns {Array} 子分段数组
   */
  splitLongSection(section, maxLength, overlap) {
    console.log(`分割长章节，原长度: ${section.length}, 最大长度: ${maxLength}`)

    const paragraphs = section.split(/\n\s*\n/).filter(p => p.trim())
    console.log(`段落数量: ${paragraphs.length}`)

    const subSegments = []
    let currentSegment = ''

    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim()

      // 检查是否需要分段
      if (currentSegment && (currentSegment.length + trimmedParagraph.length + 2) > maxLength) {
        console.log(`创建子分段，长度: ${currentSegment.length}`)
        subSegments.push(currentSegment.trim())

        // 处理重叠
        if (overlap > 0 && currentSegment.length > overlap) {
          const overlapText = this.getLastSentences(currentSegment, overlap)
          currentSegment = overlapText + '\n\n' + trimmedParagraph
        } else {
          currentSegment = trimmedParagraph
        }
      } else {
        currentSegment += (currentSegment ? '\n\n' : '') + trimmedParagraph
      }
    }

    // 添加最后一个子分段
    if (currentSegment.trim()) {
      console.log(`添加最后子分段，长度: ${currentSegment.length}`)
      subSegments.push(currentSegment.trim())
    }

    // 如果仍然没有有效分割，强制按字符分割
    if (subSegments.length === 1 && subSegments[0].length > maxLength) {
      console.log('段落分割无效，强制按字符分割')
      return this.forceCharacterSplit(section, maxLength, overlap)
    }

    console.log(`分割完成，生成 ${subSegments.length} 个子分段`)
    return subSegments
  }

  /**
   * 强制按字符分割
   * @param {string} content - 内容
   * @param {number} maxLength - 最大长度
   * @param {number} overlap - 重叠长度
   * @returns {Array} 分段数组
   */
  forceCharacterSplit(content, maxLength, overlap) {
    console.log('执行强制字符分割')
    const segments = []
    let start = 0

    while (start < content.length) {
      let end = start + maxLength

      // 尝试在句子边界分割
      if (end < content.length) {
        const nextPunctuation = content.slice(start, end + 100).search(/[。！？.!?]/)
        if (nextPunctuation !== -1 && nextPunctuation < maxLength * 0.8) {
          end = start + nextPunctuation + 1
        }
      }

      const segment = content.slice(start, Math.min(end, content.length))
      segments.push(segment.trim())

      // 计算下一个起始位置（考虑重叠）
      start = Math.max(start + 1, end - overlap)
    }

    console.log(`强制分割完成，生成 ${segments.length} 个分段`)
    return segments.filter(s => s.length > 0)
  }

  /**
   * 获取文本末尾的句子（用于重叠）
   * @param {string} text - 文本内容
   * @param {number} maxLength - 最大长度
   * @returns {string} 末尾句子
   */
  getLastSentences(text, maxLength) {
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim())
    let result = ''

    for (let i = sentences.length - 1; i >= 0; i--) {
      const sentence = sentences[i].trim()
      if (result.length + sentence.length <= maxLength) {
        result = sentence + (result ? '。' + result : '')
      } else {
        break
      }
    }

    return result || text.slice(-maxLength)
  }

  /**
   * 固定长度分段
   * @param {string} content - 文档内容
   * @param {number} maxLength - 最大长度
   * @param {number} overlap - 重叠长度
   * @returns {Array} 分段结果
   */
  fixedLengthSegment(content, maxLength, overlap) {
    const segments = []
    let segmentId = 1
    let start = 0

    while (start < content.length) {
      let end = Math.min(start + maxLength, content.length)
      
      // 尝试在单词边界处分割
      if (end < content.length) {
        const lastSpace = content.lastIndexOf(' ', end)
        const lastNewline = content.lastIndexOf('\n', end)
        const boundary = Math.max(lastSpace, lastNewline)
        
        if (boundary > start + maxLength * 0.8) {
          end = boundary
        }
      }

      const segmentContent = content.slice(start, end).trim()
      if (segmentContent) {
        segments.push(this.createSegment(segmentId++, segmentContent))
      }

      start = Math.max(end - overlap, start + 1)
    }

    return segments
  }

  /**
   * 手动分段（使用指定分隔符）
   * @param {string} content - 文档内容
   * @param {string} separator - 分隔符
   * @returns {Array} 分段结果
   */
  manualSegment(content, separator) {
    const segments = []
    const parts = content.split(separator).filter(part => part.trim())
    
    parts.forEach((part, index) => {
      segments.push(this.createSegment(index + 1, part.trim()))
    })

    return segments
  }

  /**
   * 创建分段对象
   * @param {number} id - 分段ID
   * @param {string} content - 分段内容
   * @returns {Object} 分段对象
   */
  createSegment(id, content) {
    return {
      id,
      content,
      keywords: this.extractKeywords(content),
      summary: this.generateSummary(content),
      length: content.length,
      createdAt: new Date()
    }
  }

  /**
   * 提取关键词（改进版本）
   * @param {string} content - 文本内容
   * @returns {Array} 关键词列表
   */
  extractKeywords(content) {
    // 停用词列表
    const stopWords = new Set([
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '它', '他', '她', '们', '我们', '你们', '他们', '这个', '那个', '这些', '那些', '什么', '怎么', '为什么', '哪里', '哪个', '如何', '因为', '所以', '但是', '然后', '可以', '应该', '需要', '能够', '可能', '或者', '以及', '而且', '并且', '虽然', '尽管', '如果', '假如', '除非', '直到', '当', 'while', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    ])

    // 提取中文词汇和英文单词
    const chineseWords = content.match(/[\u4e00-\u9fa5]{2,}/g) || []
    const englishWords = content.match(/[a-zA-Z]{3,}/g) || []
    const allWords = [...chineseWords, ...englishWords]

    // 统计词频
    const wordCount = {}
    allWords.forEach(word => {
      const lowerWord = word.toLowerCase()
      if (!stopWords.has(lowerWord) && word.length >= 2) {
        wordCount[word] = (wordCount[word] || 0) + 1
      }
    })

    // 计算TF-IDF权重（简化版本）
    const totalWords = allWords.length
    const keywords = Object.entries(wordCount)
      .map(([word, count]) => ({
        word,
        count,
        tf: count / totalWords,
        score: count * Math.log(content.length / word.length) // 简化的权重计算
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 8)
      .map(item => item.word)

    return keywords
  }

  /**
   * 生成摘要（改进版本）
   * @param {string} content - 文本内容
   * @returns {string} 摘要
   */
  generateSummary(content) {
    try {
      console.log('开始生成摘要，内容长度:', content.length)

      if (!content || content.trim().length === 0) {
        console.warn('内容为空，无法生成摘要')
        return '内容为空'
      }

      // 清理文本
      const cleanContent = content.replace(/\s+/g, ' ').trim()

      // 如果内容很短，直接返回
      if (cleanContent.length <= 150) {
        console.log('内容较短，直接返回原内容')
        return cleanContent
      }

      // 按句子分割
      const sentences = cleanContent.split(/[。！？.!?]/).filter(s => s.trim().length > 10)
      console.log('分割句子数量:', sentences.length)

      if (sentences.length <= 2) {
        // 如果句子很少，返回前150个字符
        const shortSummary = cleanContent.substring(0, 150) + '...'
        console.log('句子较少，返回截取摘要')
        return shortSummary
      }

    // 计算句子重要性得分
    const sentenceScores = sentences.map(sentence => {
      const words = sentence.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g) || []
      const keywords = this.extractKeywords(content)

      // 计算句子中关键词的密度
      let keywordCount = 0
      words.forEach(word => {
        if (keywords.includes(word)) {
          keywordCount++
        }
      })

      const score = {
        sentence: sentence.trim(),
        keywordDensity: keywordCount / words.length,
        position: sentences.indexOf(sentence), // 位置权重
        length: sentence.length
      }

      // 综合得分：关键词密度 + 位置权重（开头和结尾的句子权重更高）
      const positionWeight = sentence === sentences[0] ? 1.5 :
                           sentence === sentences[sentences.length - 1] ? 1.2 : 1.0
      score.totalScore = score.keywordDensity * positionWeight

      return score
    })

    // 选择得分最高的句子作为摘要
    const topSentences = sentenceScores
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, 2)
      .sort((a, b) => a.position - b.position) // 按原始顺序排列
      .map(s => s.sentence)

    let summary = topSentences.join('。')

    // 确保摘要长度合适
    if (summary.length > 200) {
      summary = summary.substring(0, 200) + '...'
    } else if (!summary.endsWith('。') && !summary.endsWith('...')) {
      summary += '。'
    }

    console.log('摘要生成完成，长度:', summary.length)
    return summary

    } catch (error) {
      console.error('摘要生成失败:', error)
      // 回退到简单截取
      const fallbackSummary = content.substring(0, 100) + '...'
      console.log('使用回退摘要:', fallbackSummary)
      return fallbackSummary
    }
  }
}

export default new DocumentParser()
