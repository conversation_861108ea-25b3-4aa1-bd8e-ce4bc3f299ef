/**
 * 增强版文档解析器
 * 结合本地处理和阿里云百炼服务的混合架构
 */

import DocumentParser from './documentParser.js'
import BailianService from '../services/bailianService.js'

class EnhancedDocumentParser {
  constructor(config = {}) {
    this.localParser = DocumentParser
    this.bailianService = new BailianService(config.bailian)
    this.config = {
      useAI: config.useAI !== false, // 默认启用AI增强
      fallbackToLocal: config.fallbackToLocal !== false, // 默认启用本地备选
      maxFileSize: config.maxFileSize || 10 * 1024 * 1024, // 10MB
      aiTimeout: config.aiTimeout || 30000, // 30秒超时
      ...config
    }
  }

  /**
   * 解析文档内容
   * @param {File} file - 文件对象
   * @param {Object} options - 解析选项
   * @returns {Promise<Object>} 解析结果
   */
  async parseDocument(file) {
    const startTime = Date.now()
    
    try {
      // 基础文件验证
      this.validateFile(file)
      
      // 使用本地解析器获取文本内容
      const content = await this.localParser.parseDocument(file)
      
      // 文档分析（如果启用AI）
      let analysis = null
      if (this.config.useAI && this.bailianService.isEnabled()) {
        try {
          analysis = await Promise.race([
            this.bailianService.analyzeDocument(content),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('AI分析超时')), this.config.aiTimeout)
            )
          ])
        } catch (error) {
          console.warn('AI文档分析失败，使用本地分析:', error.message)
          analysis = this.getLocalAnalysis(content)
        }
      } else {
        analysis = this.getLocalAnalysis(content)
      }

      const parseTime = Date.now() - startTime

      return {
        fileName: file.name,
        fileSize: file.size,
        fileType: this.localParser.getFileType(file),
        content: content,
        analysis: analysis,
        parseTime: parseTime,
        source: 'enhanced',
        timestamp: new Date()
      }

    } catch (error) {
      console.error('文档解析失败:', error)
      throw new Error(`文档解析失败: ${error.message}`)
    }
  }

  /**
   * 智能分段处理
   * @param {string} content - 文档内容
   * @param {Object} settings - 分段设置
   * @param {Object} analysis - 文档分析结果
   * @returns {Promise<Array>} 分段结果
   */
  async segmentDocument(content, settings = {}, analysis = null) {
    const {
      strategy = 'auto',
      maxLength = 1000,
      useAI = this.config.useAI
    } = settings

    try {
      // 如果启用AI且服务可用，优先使用AI分段
      if (useAI && this.bailianService.isEnabled()) {
        try {
          const aiSegments = await Promise.race([
            this.bailianService.segmentDocument(content, {
              maxLength,
              strategy: this.getAIStrategy(strategy, analysis)
            }),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('AI分段超时')), this.config.aiTimeout)
            )
          ])

          // AI分段成功，进行后处理
          return this.postProcessSegments(aiSegments, settings)

        } catch (error) {
          console.warn('AI分段失败，回退到本地处理:', error.message)
          
          if (!this.config.fallbackToLocal) {
            throw error
          }
        }
      }

      // 使用本地分段
      const localSegments = this.localParser.segmentDocument(content, settings)
      
      // 如果启用AI，尝试增强本地分段结果
      if (useAI && this.bailianService.isEnabled() && localSegments.length > 0) {
        try {
          return await this.enhanceLocalSegments(localSegments)
        } catch (enhanceError) {
          console.warn('AI增强失败，返回本地分段结果:', enhanceError.message)
          return localSegments
        }
      }

      return localSegments

    } catch (error) {
      console.error('分段处理失败:', error)
      // 最后的回退：使用最简单的固定长度分段
      console.log('使用最简单的固定长度分段作为最后回退')
      try {
        return this.localParser.fixedLengthSegment(content, maxLength, 100)
      } catch (fallbackError) {
        console.error('固定长度分段也失败:', fallbackError)
        throw error
      }
    }
  }

  /**
   * 增强本地分段结果
   * @param {Array} segments - 本地分段结果
   * @returns {Promise<Array>} 增强后的分段结果
   */
  async enhanceLocalSegments(segments) {
    const enhancedSegments = []

    for (const segment of segments) {
      try {
        // 并行处理关键词提取和摘要生成
        const [keywords, summary] = await Promise.all([
          this.bailianService.extractKeywords(segment.content).catch((error) => {
            console.log('关键词提取失败，使用默认值:', error.message)
            return segment.keywords || []
          }),
          this.bailianService.generateSummary(segment.content).catch((error) => {
            console.log('摘要生成失败，使用默认值:', error.message)
            return segment.summary || ''
          })
        ])

        enhancedSegments.push({
          ...segment,
          keywords: keywords.length > 0 ? keywords : segment.keywords,
          summary: summary || segment.summary,
          enhanced: true,
          source: 'hybrid'
        })

        // 添加延迟避免API限流
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.warn(`增强分段 ${segment.id} 失败:`, error.message)
        enhancedSegments.push({
          ...segment,
          enhanced: false,
          source: 'local'
        })
      }
    }

    return enhancedSegments
  }

  /**
   * 后处理分段结果
   * @param {Array} segments - 原始分段
   * @param {Object} settings - 设置
   * @returns {Array} 处理后的分段
   */
  postProcessSegments(segments, settings) {
    return segments.map((segment, index) => ({
      ...segment,
      id: segment.id || index + 1,
      length: segment.content ? segment.content.length : 0,
      createdAt: segment.createdAt || new Date(),
      settings: {
        strategy: settings.strategy,
        maxLength: settings.maxLength,
        overlap: settings.overlap
      }
    }))
  }

  /**
   * 获取AI分段策略
   * @param {string} userStrategy - 用户选择的策略
   * @param {Object} analysis - 文档分析结果
   * @returns {string} AI分段策略
   */
  getAIStrategy(userStrategy, analysis) {
    if (userStrategy !== 'auto') {
      return userStrategy
    }

    if (analysis && analysis.recommendedStrategy) {
      return analysis.recommendedStrategy
    }

    // 根据文档类型推荐策略
    if (analysis && analysis.documentType) {
      const type = analysis.documentType.toLowerCase()
      if (type.includes('技术') || type.includes('manual')) {
        return 'semantic'
      } else if (type.includes('学术') || type.includes('论文')) {
        return 'structured'
      } else if (type.includes('新闻') || type.includes('报道')) {
        return 'paragraph'
      }
    }

    return 'semantic'
  }

  /**
   * 本地文档分析
   * @param {string} content - 文档内容
   * @returns {Object} 分析结果
   */
  getLocalAnalysis(content) {
    const lines = content.split('\n')
    const words = content.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g) || []
    
    // 简单的文档类型判断
    let documentType = '通用文档'
    if (content.includes('摘要') || content.includes('Abstract')) {
      documentType = '学术论文'
    } else if (content.includes('API') || content.includes('接口') || content.includes('函数')) {
      documentType = '技术文档'
    } else if (content.includes('新闻') || content.includes('报道')) {
      documentType = '新闻报道'
    }

    // 结构完整性评分
    const hasHeaders = /^#{1,6}\s+|^第[一二三四五六七八九十\d]+[章节]/.test(content)
    const hasParagraphs = lines.filter(line => line.trim()).length > 5
    const structureScore = (hasHeaders ? 5 : 0) + (hasParagraphs ? 3 : 0) + 2

    // 内容质量评分
    const avgWordsPerLine = words.length / lines.length
    const qualityScore = Math.min(10, Math.max(1, Math.round(avgWordsPerLine / 2 + 3)))

    return {
      documentType,
      structureScore: Math.min(10, structureScore),
      qualityScore,
      recommendedStrategy: hasHeaders ? 'semantic' : 'auto',
      mainTopics: this.extractMainTopics(content),
      statistics: {
        totalLines: lines.length,
        totalWords: words.length,
        avgWordsPerLine: Math.round(avgWordsPerLine)
      }
    }
  }

  /**
   * 提取主要主题
   * @param {string} content - 文档内容
   * @returns {Array} 主题列表
   */
  extractMainTopics(content) {
    const topics = []
    
    // 提取标题作为主题
    const headers = content.match(/^#{1,6}\s+(.+)$/gm) || []
    headers.forEach(header => {
      const topic = header.replace(/^#{1,6}\s+/, '').trim()
      if (topic && topic.length < 50) {
        topics.push(topic)
      }
    })

    // 如果没有标题，提取高频词汇
    if (topics.length === 0) {
      const words = content.match(/[\u4e00-\u9fa5]{2,}/g) || []
      const wordCount = {}
      
      words.forEach(word => {
        if (word.length >= 2 && word.length <= 6) {
          wordCount[word] = (wordCount[word] || 0) + 1
        }
      })

      const topWords = Object.entries(wordCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([word]) => word)
      
      topics.push(...topWords)
    }

    return topics.slice(0, 8)
  }

  /**
   * 文件验证
   * @param {File} file - 文件对象
   */
  validateFile(file) {
    if (!file) {
      throw new Error('文件不能为空')
    }

    if (file.size > this.config.maxFileSize) {
      throw new Error(`文件大小超过限制 (${this.config.maxFileSize / 1024 / 1024}MB)`)
    }

    const supportedTypes = [
      'text/plain',
      'text/markdown',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]

    const fileType = this.localParser.getFileType(file)
    if (!supportedTypes.some(type => this.localParser.supportedTypes[type] === fileType)) {
      throw new Error(`不支持的文件类型: ${file.type}`)
    }
  }

  /**
   * 批量处理文档
   * @param {Array} files - 文件列表
   * @param {Object} settings - 处理设置
   * @param {Function} progressCallback - 进度回调
   * @returns {Promise<Array>} 处理结果
   */
  async batchProcess(files, settings = {}, progressCallback) {
    const results = []
    const total = files.length

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      try {
        if (progressCallback) {
          progressCallback({
            current: i + 1,
            total,
            status: 'parsing',
            fileName: file.name
          })
        }

        // 解析文档
        const parseResult = await this.parseDocument(file, settings)
        
        if (progressCallback) {
          progressCallback({
            current: i + 1,
            total,
            status: 'segmenting',
            fileName: file.name
          })
        }

        // 分段处理
        const segments = await this.segmentDocument(
          parseResult.content, 
          settings, 
          parseResult.analysis
        )

        results.push({
          ...parseResult,
          segments,
          status: 'completed'
        })

      } catch (error) {
        console.error(`处理文件 ${file.name} 失败:`, error)
        results.push({
          fileName: file.name,
          fileSize: file.size,
          status: 'error',
          error: error.message,
          segments: []
        })
      }
    }

    if (progressCallback) {
      progressCallback({
        current: total,
        total,
        status: 'completed'
      })
    }

    return results
  }

  /**
   * 获取处理统计信息
   * @param {Array} results - 处理结果
   * @returns {Object} 统计信息
   */
  getProcessingStats(results) {
    const stats = {
      total: results.length,
      completed: 0,
      failed: 0,
      totalSegments: 0,
      totalWords: 0,
      avgSegmentsPerDoc: 0,
      processingTime: 0
    }

    results.forEach(result => {
      if (result.status === 'completed') {
        stats.completed++
        stats.totalSegments += result.segments ? result.segments.length : 0
        stats.totalWords += result.content ? result.content.length : 0
        stats.processingTime += result.parseTime || 0
      } else {
        stats.failed++
      }
    })

    stats.avgSegmentsPerDoc = stats.completed > 0 ? 
      Math.round(stats.totalSegments / stats.completed) : 0

    return stats
  }
}

export default EnhancedDocumentParser
